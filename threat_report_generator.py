"""
Threat Report Generator Module

Generates professional PDF and CSV reports from threat analysis results.
"""

import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import csv
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.lib.colors import HexColor


class ThreatReportGenerator:
    """
    Generates professional reports from threat analysis results.
    Supports both PDF and CSV export formats.
    """
    
    def __init__(self):
        """Initialize the report generator."""
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the report."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.darkblue,
            alignment=1  # Center alignment
        ))
        
        # Heading style
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkred,
            borderWidth=1,
            borderColor=colors.darkred,
            borderPadding=5
        ))
        
        # Subheading style
        self.styles.add(ParagraphStyle(
            name='CustomSubHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            textColor=colors.darkblue
        ))
        
        # Alert style for high-priority items
        self.styles.add(ParagraphStyle(
            name='AlertStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            textColor=colors.red,
            backColor=colors.lightgrey,
            borderWidth=1,
            borderColor=colors.red,
            borderPadding=5
        ))
    
    def generate_pdf_report(self, analysis_results: Dict[str, Any], output_path: str, 
                          log_filename: str = "Unknown") -> bool:
        """
        Generate a comprehensive PDF report from analysis results.
        
        Args:
            analysis_results: Results from ThreatLogAnalyzer
            output_path: Path where PDF should be saved
            log_filename: Name of the original log file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create PDF document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build report content
            story = []
            
            # Title page
            story.extend(self._create_title_page(log_filename))
            
            # Executive summary
            story.extend(self._create_executive_summary(analysis_results))
            
            # Detailed analysis sections
            story.extend(self._create_threat_detection_section(analysis_results))
            story.extend(self._create_statistical_analysis_section(analysis_results))
            story.extend(self._create_correlation_analysis_section(analysis_results))
            story.extend(self._create_recommendations_section(analysis_results))
            
            # Build PDF
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"Error generating PDF report: {e}")
            return False
    
    def _create_title_page(self, log_filename: str) -> List:
        """Create the title page content."""
        story = []
        
        # Main title
        title = Paragraph("🛡️ Firewall Threat Log Analysis Report", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 0.5*inch))
        
        # Report details
        report_info = f"""
        <b>Log File:</b> {log_filename}<br/>
        <b>Analysis Date:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>
        <b>Generated by:</b> CSO Tools - Threat Log Analyzer<br/>
        <b>Report Version:</b> 1.0
        """
        
        info_para = Paragraph(report_info, self.styles['Normal'])
        story.append(info_para)
        story.append(Spacer(1, 1*inch))
        
        # Disclaimer
        disclaimer = """
        <b>CONFIDENTIAL SECURITY REPORT</b><br/><br/>
        This report contains sensitive security information and should be handled according to 
        your organization's data classification policies. The analysis is based on automated 
        threat detection rules and should be validated by security professionals.
        """
        
        disclaimer_para = Paragraph(disclaimer, self.styles['AlertStyle'])
        story.append(disclaimer_para)
        story.append(PageBreak())
        
        return story
    
    def _create_executive_summary(self, analysis_results: Dict[str, Any]) -> List:
        """Create executive summary section."""
        story = []
        
        # Section header
        header = Paragraph("📊 Executive Summary", self.styles['CustomHeading'])
        story.append(header)
        
        summary = analysis_results.get('summary', {})
        threat_detections = analysis_results.get('threat_detections', [])
        
        # Key metrics
        total_logs = summary.get('total_log_entries', 0)
        total_threats = sum(t['count'] for t in threat_detections)
        high_priority_threats = len([t for t in threat_detections if t['priority'] == 'HIGH'])
        
        # Create summary table
        summary_data = [
            ['Metric', 'Value'],
            ['Total Log Entries Analyzed', f"{total_logs:,}"],
            ['Total Threats Detected', f"{total_threats:,}"],
            ['High Priority Threats', f"{high_priority_threats:,}"],
            ['Unique Source IPs', f"{summary.get('unique_source_ips', 0):,}"],
            ['Unique Destination IPs', f"{summary.get('unique_destination_ips', 0):,}"]
        ]
        
        # Time range information
        time_range = summary.get('analysis_time_range', {})
        if time_range.get('start') and time_range.get('end'):
            start_time = time_range['start'].strftime('%Y-%m-%d %H:%M:%S')
            end_time = time_range['end'].strftime('%Y-%m-%d %H:%M:%S')
            summary_data.extend([
                ['Analysis Start Time', start_time],
                ['Analysis End Time', end_time]
            ])
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 0.3*inch))
        
        # Risk assessment
        risk_level = "LOW"
        if high_priority_threats > 0:
            risk_level = "HIGH"
        elif total_threats > total_logs * 0.05:  # More than 5% threats
            risk_level = "MEDIUM"
        
        risk_colors = {"LOW": colors.green, "MEDIUM": colors.orange, "HIGH": colors.red}
        risk_text = f"<b>Overall Risk Assessment: <font color='{risk_colors[risk_level]}'>{risk_level}</font></b>"
        risk_para = Paragraph(risk_text, self.styles['Normal'])
        story.append(risk_para)
        story.append(Spacer(1, 0.3*inch))
        
        return story
    
    def _create_threat_detection_section(self, analysis_results: Dict[str, Any]) -> List:
        """Create threat detection results section."""
        story = []
        
        # Section header
        header = Paragraph("🚨 Threat Detection Results", self.styles['CustomHeading'])
        story.append(header)
        
        threat_detections = analysis_results.get('threat_detections', [])
        
        if not threat_detections:
            no_threats = Paragraph("No threats detected in the analyzed logs.", self.styles['Normal'])
            story.append(no_threats)
            story.append(Spacer(1, 0.3*inch))
            return story
        
        # Sort threats by priority and count
        priority_order = {'HIGH': 0, 'MEDIUM': 1, 'LOW': 2}
        sorted_threats = sorted(
            threat_detections, 
            key=lambda x: (priority_order.get(x['priority'], 3), -x['count'])
        )
        
        for threat in sorted_threats:
            # Threat name and priority
            priority_color = {'HIGH': 'red', 'MEDIUM': 'orange', 'LOW': 'green'}.get(threat['priority'], 'black')
            threat_title = f"<b>{threat['rule_name']}</b> " \
                          f"(<font color='{priority_color}'>{threat['priority']} Priority</font>)"
            
            title_para = Paragraph(threat_title, self.styles['CustomSubHeading'])
            story.append(title_para)
            
            # Description and count
            desc_text = f"<b>Description:</b> {threat['description']}<br/>" \
                       f"<b>Matches Found:</b> {threat['count']}"
            
            desc_para = Paragraph(desc_text, self.styles['Normal'])
            story.append(desc_para)
            
            # Sample matches table (if available)
            matches = threat.get('matches', [])
            if matches:
                story.append(Spacer(1, 0.1*inch))
                
                # Create table with sample matches
                table_data = [['Source IP', 'Destination IP', 'Action', 'Threat Name']]
                
                for match in matches[:5]:  # Show first 5 matches
                    table_data.append([
                        str(match.get('source_ip', 'Unknown'))[:20],
                        str(match.get('destination_ip', 'Unknown'))[:20],
                        str(match.get('action', 'Unknown')),
                        str(match.get('threat_name', 'Unknown'))[:30]
                    ])
                
                matches_table = Table(table_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 2*inch])
                matches_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP')
                ]))
                
                story.append(matches_table)
            
            story.append(Spacer(1, 0.2*inch))
        
        return story

    def _create_statistical_analysis_section(self, analysis_results: Dict[str, Any]) -> List:
        """Create statistical analysis section."""
        story = []

        # Section header
        header = Paragraph("📈 Statistical Analysis", self.styles['CustomHeading'])
        story.append(header)

        stats = analysis_results.get('statistical_analysis', {})

        # Top denied source IPs
        if 'top_denied_source_ips' in stats:
            subheader = Paragraph("Top Source IPs with Denied Connections", self.styles['CustomSubHeading'])
            story.append(subheader)

            denied_ips = stats['top_denied_source_ips']
            if denied_ips:
                table_data = [['Source IP', 'Denied Connections']]
                for ip, count in list(denied_ips.items())[:10]:
                    table_data.append([str(ip), str(count)])

                ip_table = Table(table_data, colWidths=[3*inch, 2*inch])
                ip_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(ip_table)
                story.append(Spacer(1, 0.2*inch))

        # Top threat types
        if 'top_threat_types' in stats:
            subheader = Paragraph("Most Common Threat Types", self.styles['CustomSubHeading'])
            story.append(subheader)

            threat_types = stats['top_threat_types']
            if threat_types:
                table_data = [['Threat Type', 'Occurrences']]
                for threat_type, count in list(threat_types.items())[:10]:
                    table_data.append([str(threat_type)[:40], str(count)])

                threat_table = Table(table_data, colWidths=[4*inch, 1*inch])
                threat_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkorange),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(threat_table)
                story.append(Spacer(1, 0.2*inch))

        return story

    def _create_correlation_analysis_section(self, analysis_results: Dict[str, Any]) -> List:
        """Create correlation analysis section."""
        story = []

        # Section header
        header = Paragraph("🔗 Correlation Analysis", self.styles['CustomHeading'])
        story.append(header)

        correlations = analysis_results.get('correlation_analysis', {})

        # High activity sources
        if 'high_activity_sources' in correlations:
            subheader = Paragraph("High Activity Source IPs", self.styles['CustomSubHeading'])
            story.append(subheader)

            high_activity = correlations['high_activity_sources']
            if high_activity:
                desc = Paragraph(
                    "Source IPs with unusually high connection attempts (>10 connections):",
                    self.styles['Normal']
                )
                story.append(desc)

                table_data = [['Source IP', 'Total Connections']]
                for ip, count in list(high_activity.items())[:10]:
                    table_data.append([str(ip), str(count)])

                activity_table = Table(table_data, colWidths=[3*inch, 2*inch])
                activity_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(activity_table)
                story.append(Spacer(1, 0.2*inch))

        return story

    def _create_recommendations_section(self, analysis_results: Dict[str, Any]) -> List:
        """Create recommendations section."""
        story = []

        # Section header
        header = Paragraph("💡 Security Recommendations", self.styles['CustomHeading'])
        story.append(header)

        recommendations = analysis_results.get('recommendations', [])

        if not recommendations:
            no_rec = Paragraph("No specific recommendations generated.", self.styles['Normal'])
            story.append(no_rec)
            return story

        for i, rec in enumerate(recommendations, 1):
            # Determine if this is a high-priority recommendation
            is_urgent = rec.upper().startswith('URGENT')
            style = self.styles['AlertStyle'] if is_urgent else self.styles['Normal']

            rec_text = f"<b>{i}.</b> {rec}"
            rec_para = Paragraph(rec_text, style)
            story.append(rec_para)
            story.append(Spacer(1, 0.1*inch))

        return story

    def export_analysis_to_csv(self, analysis_results: Dict[str, Any], output_path: str) -> bool:
        """
        Export analysis results to CSV format for further analysis.

        Args:
            analysis_results: Results from ThreatLogAnalyzer
            output_path: Path where CSV should be saved

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow(['Firewall Threat Log Analysis Export'])
                writer.writerow(['Generated on:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                writer.writerow([])  # Empty row

                # Summary statistics
                writer.writerow(['=== SUMMARY STATISTICS ==='])
                summary = analysis_results.get('summary', {})

                writer.writerow(['Total Log Entries', summary.get('total_log_entries', 0)])
                writer.writerow(['Unique Source IPs', summary.get('unique_source_ips', 0)])
                writer.writerow(['Unique Destination IPs', summary.get('unique_destination_ips', 0)])
                writer.writerow([])

                # Threat detections
                writer.writerow(['=== THREAT DETECTIONS ==='])
                writer.writerow(['Rule Name', 'Priority', 'Count', 'Description'])

                threat_detections = analysis_results.get('threat_detections', [])
                for threat in threat_detections:
                    writer.writerow([
                        threat['rule_name'],
                        threat['priority'],
                        threat['count'],
                        threat['description']
                    ])

                writer.writerow([])

                # Recommendations
                writer.writerow(['=== RECOMMENDATIONS ==='])
                recommendations = analysis_results.get('recommendations', [])
                for i, rec in enumerate(recommendations, 1):
                    writer.writerow([f'Recommendation {i}', rec])

            return True

        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False
