"""
Firewall Threat Log Analyzer Module

This module provides comprehensive analysis of Palo Alto firewall logs
including threat detection, pattern analysis, and report generation.
"""

import pandas as pd
import re
import csv
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from collections import Counter, defaultdict
import ipaddress
import json


class ThreatLogAnalyzer:
    """
    Main class for analyzing Palo Alto firewall threat logs.
    Handles parsing, normalization, analysis, and reporting.
    """
    
    def __init__(self):
        """Initialize the threat log analyzer."""
        self.raw_data: Optional[pd.DataFrame] = None
        self.normalized_data: Optional[pd.DataFrame] = None
        self.analysis_results: Dict[str, Any] = {}
        self.threat_rules: List[Dict] = self._initialize_threat_rules()
        
    def _initialize_threat_rules(self) -> List[Dict]:
        """Initialize predefined threat detection rules."""
        return [
            {
                'name': 'High Severity Threats',
                'condition': lambda row: row.get('Severity', '').lower() in ['high', 'critical'],
                'priority': 'HIGH',
                'description': 'Threats with high or critical severity levels'
            },
            {
                'name': 'Malware Detection',
                'condition': lambda row: 'malware' in str(row.get('Category', '')).lower(),
                'priority': 'HIGH',
                'description': 'Malware-related threats detected'
            },
            {
                'name': 'Denied External Connections',
                'condition': lambda row: (
                    row.get('Action', '').lower() == 'deny' and
                    self._is_external_ip(row.get('Source address', ''))
                ),
                'priority': 'MEDIUM',
                'description': 'Denied connections from external IP addresses'
            },
            {
                'name': 'Suspicious Port Activity',
                'condition': lambda row: self._is_suspicious_port(row.get('Destination Port', '')),
                'priority': 'MEDIUM',
                'description': 'Activity on commonly exploited ports'
            },
            {
                'name': 'Multiple Failed Attempts',
                'condition': lambda row: int(row.get('Repeat Count', 0) or 0) > 5,
                'priority': 'MEDIUM',
                'description': 'Multiple repeated connection attempts'
            }
        ]
    
    def _is_external_ip(self, ip_str: str) -> bool:
        """Check if an IP address is external (not private)."""
        try:
            ip = ipaddress.ip_address(ip_str.strip())
            return not ip.is_private
        except (ValueError, AttributeError):
            return False
    
    def _is_suspicious_port(self, port_str: str) -> bool:
        """Check if a port is commonly associated with threats."""
        suspicious_ports = {
            22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995,
            1433, 1521, 3306, 3389, 5432, 5900, 6379, 27017
        }
        try:
            port = int(port_str)
            return port in suspicious_ports
        except (ValueError, TypeError):
            return False
    
    def load_log_file(self, file_path: str) -> bool:
        """
        Load and parse Palo Alto log CSV file.
        
        Args:
            file_path: Path to the CSV log file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Read CSV with error handling for malformed data
            self.raw_data = pd.read_csv(
                file_path,
                encoding='utf-8',
                low_memory=False,
                on_bad_lines='skip'
            )
            
            # Basic validation
            if self.raw_data.empty:
                raise ValueError("CSV file is empty")
            
            # Normalize the data
            self._normalize_data()
            
            return True
            
        except Exception as e:
            print(f"Error loading log file: {e}")
            return False
    
    def _normalize_data(self) -> None:
        """Normalize and clean the loaded data."""
        if self.raw_data is None:
            return
        
        # Create a copy for normalization
        self.normalized_data = self.raw_data.copy()
        
        # Convert timestamps
        timestamp_columns = ['Receive Time', 'Generate Time', 'Time Logged']
        for col in timestamp_columns:
            if col in self.normalized_data.columns:
                self.normalized_data[col] = pd.to_datetime(
                    self.normalized_data[col], 
                    errors='coerce'
                )
        
        # Convert numeric columns
        numeric_columns = ['Source Port', 'Destination Port', 'Repeat Count', 'Session ID']
        for col in numeric_columns:
            if col in self.normalized_data.columns:
                self.normalized_data[col] = pd.to_numeric(
                    self.normalized_data[col], 
                    errors='coerce'
                )
        
        # Clean and standardize text fields
        text_columns = ['Action', 'Severity', 'Category', 'Direction']
        for col in text_columns:
            if col in self.normalized_data.columns:
                self.normalized_data[col] = (
                    self.normalized_data[col]
                    .astype(str)
                    .str.strip()
                    .str.lower()
                )
        
        # Fill NaN values with appropriate defaults
        self.normalized_data = self.normalized_data.fillna({
            'Action': 'unknown',
            'Severity': 'low',
            'Category': 'unknown',
            'Direction': 'unknown',
            'Repeat Count': 0
        })
    
    def analyze_threats(self) -> Dict[str, Any]:
        """
        Perform comprehensive threat analysis on the normalized data.
        
        Returns:
            Dictionary containing analysis results
        """
        if self.normalized_data is None or self.normalized_data.empty:
            return {}
        
        # Initialize analysis results
        self.analysis_results = {
            'summary': self._generate_summary(),
            'threat_detections': self._detect_threats(),
            'statistical_analysis': self._perform_statistical_analysis(),
            'correlation_analysis': self._perform_correlation_analysis(),
            'recommendations': []
        }
        
        # Generate recommendations based on findings
        self.analysis_results['recommendations'] = self._generate_recommendations()
        
        return self.analysis_results
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary statistics from the log data."""
        total_logs = len(self.normalized_data)
        
        # Time range analysis
        time_col = 'Receive Time'
        if time_col in self.normalized_data.columns:
            time_data = self.normalized_data[time_col].dropna()
            if not time_data.empty:
                start_time = time_data.min()
                end_time = time_data.max()
                duration = end_time - start_time
            else:
                start_time = end_time = duration = None
        else:
            start_time = end_time = duration = None
        
        # Action analysis
        action_counts = self.normalized_data['Action'].value_counts().to_dict()
        
        # Severity analysis
        severity_counts = self.normalized_data['Severity'].value_counts().to_dict()
        
        return {
            'total_log_entries': total_logs,
            'analysis_time_range': {
                'start': start_time,
                'end': end_time,
                'duration': duration
            },
            'action_distribution': action_counts,
            'severity_distribution': severity_counts,
            'unique_source_ips': self.normalized_data['Source address'].nunique(),
            'unique_destination_ips': self.normalized_data['Destination address'].nunique()
        }
    
    def _detect_threats(self) -> List[Dict[str, Any]]:
        """Apply threat detection rules to identify potential threats."""
        detected_threats = []
        
        for rule in self.threat_rules:
            matching_logs = []
            
            for idx, row in self.normalized_data.iterrows():
                try:
                    if rule['condition'](row):
                        matching_logs.append({
                            'log_index': idx,
                            'source_ip': row.get('Source address', 'Unknown'),
                            'destination_ip': row.get('Destination address', 'Unknown'),
                            'action': row.get('Action', 'Unknown'),
                            'severity': row.get('Severity', 'Unknown'),
                            'threat_name': row.get('Threat/Content Name', 'Unknown'),
                            'timestamp': row.get('Receive Time', 'Unknown')
                        })
                except Exception as e:
                    # Skip problematic rows
                    continue
            
            if matching_logs:
                detected_threats.append({
                    'rule_name': rule['name'],
                    'priority': rule['priority'],
                    'description': rule['description'],
                    'count': len(matching_logs),
                    'matches': matching_logs[:10]  # Limit to first 10 matches for performance
                })
        
        return detected_threats

    def _perform_statistical_analysis(self) -> Dict[str, Any]:
        """Perform statistical analysis on the log data."""
        stats = {}

        # Top source IPs with denied actions
        denied_logs = self.normalized_data[self.normalized_data['Action'] == 'deny']
        if not denied_logs.empty:
            top_denied_sources = denied_logs['Source address'].value_counts().head(10).to_dict()
            stats['top_denied_source_ips'] = top_denied_sources

        # Top threat types
        threat_types = self.normalized_data['Threat/Content Type'].value_counts().head(10).to_dict()
        stats['top_threat_types'] = threat_types

        # Top destination ports
        dest_ports = self.normalized_data['Destination Port'].value_counts().head(10).to_dict()
        stats['top_destination_ports'] = dest_ports

        # Geographic analysis (if country data available)
        if 'Source Country' in self.normalized_data.columns:
            source_countries = self.normalized_data['Source Country'].value_counts().head(10).to_dict()
            stats['top_source_countries'] = source_countries

        # Time-based analysis
        if 'Receive Time' in self.normalized_data.columns:
            time_data = self.normalized_data['Receive Time'].dropna()
            if not time_data.empty:
                # Hourly distribution
                hourly_dist = time_data.dt.hour.value_counts().sort_index().to_dict()
                stats['hourly_distribution'] = hourly_dist

                # Daily distribution
                daily_dist = time_data.dt.date.value_counts().head(10).to_dict()
                # Convert date objects to strings for JSON serialization
                daily_dist = {str(k): v for k, v in daily_dist.items()}
                stats['daily_distribution'] = daily_dist

        return stats

    def _perform_correlation_analysis(self) -> Dict[str, Any]:
        """Perform correlation analysis to identify related events."""
        correlations = {}

        # Source IP correlation - multiple attempts from same IP
        source_ip_counts = self.normalized_data['Source address'].value_counts()
        suspicious_sources = source_ip_counts[source_ip_counts > 10].to_dict()
        correlations['high_activity_sources'] = suspicious_sources

        # Destination IP correlation - multiple attacks to same target
        dest_ip_counts = self.normalized_data['Destination address'].value_counts()
        targeted_destinations = dest_ip_counts[dest_ip_counts > 5].to_dict()
        correlations['targeted_destinations'] = targeted_destinations

        # Time-based correlation - events within short time windows
        if 'Receive Time' in self.normalized_data.columns:
            time_data = self.normalized_data['Receive Time'].dropna()
            if not time_data.empty:
                # Group events by 5-minute windows
                time_groups = time_data.dt.floor('5min').value_counts()
                burst_periods = time_groups[time_groups > 20].to_dict()
                # Convert timestamps to strings
                burst_periods = {str(k): v for k, v in burst_periods.items()}
                correlations['burst_periods'] = burst_periods

        return correlations

    def _generate_recommendations(self) -> List[str]:
        """Generate security recommendations based on analysis results."""
        recommendations = []

        # Check for high-severity threats
        threat_detections = self.analysis_results.get('threat_detections', [])
        high_priority_threats = [t for t in threat_detections if t['priority'] == 'HIGH']

        if high_priority_threats:
            recommendations.append(
                f"URGENT: {len(high_priority_threats)} high-priority threats detected. "
                "Immediate investigation required."
            )

        # Check for denied connections
        summary = self.analysis_results.get('summary', {})
        action_dist = summary.get('action_distribution', {})
        denied_count = action_dist.get('deny', 0)
        total_count = summary.get('total_log_entries', 1)

        if denied_count > total_count * 0.1:  # More than 10% denied
            recommendations.append(
                f"High denial rate detected ({denied_count} denied out of {total_count} total). "
                "Review firewall rules and investigate potential attack patterns."
            )

        # Check for geographic anomalies
        stats = self.analysis_results.get('statistical_analysis', {})
        source_countries = stats.get('top_source_countries', {})

        high_risk_countries = ['CN', 'RU', 'KP', 'IR']  # Example high-risk countries
        for country, count in source_countries.items():
            if country in high_risk_countries and count > 10:
                recommendations.append(
                    f"High activity from high-risk country {country} ({count} events). "
                    "Consider implementing geo-blocking or enhanced monitoring."
                )

        # Check for correlation patterns
        correlations = self.analysis_results.get('correlation_analysis', {})
        high_activity_sources = correlations.get('high_activity_sources', {})

        if high_activity_sources:
            top_source = max(high_activity_sources.items(), key=lambda x: x[1])
            recommendations.append(
                f"Source IP {top_source[0]} shows unusually high activity ({top_source[1]} events). "
                "Investigate for potential compromise or scanning activity."
            )

        # Default recommendation if no specific issues found
        if not recommendations:
            recommendations.append(
                "No immediate security concerns identified. Continue regular monitoring."
            )

        return recommendations

    def get_analysis_summary(self) -> str:
        """Generate a text summary of the analysis results."""
        if not self.analysis_results:
            return "No analysis results available."

        summary = self.analysis_results.get('summary', {})
        threat_detections = self.analysis_results.get('threat_detections', [])

        total_logs = summary.get('total_log_entries', 0)
        total_threats = sum(t['count'] for t in threat_detections)

        summary_text = f"""
🔍 Firewall Threat Log Analysis Summary

📊 Overview:
• Total Log Entries: {total_logs:,}
• Threats Detected: {total_threats:,}
• Unique Source IPs: {summary.get('unique_source_ips', 0):,}
• Unique Destination IPs: {summary.get('unique_destination_ips', 0):,}

🚨 Threat Detection Results:
"""

        for threat in threat_detections:
            summary_text += f"• {threat['rule_name']}: {threat['count']} matches ({threat['priority']} priority)\n"

        summary_text += "\n💡 Recommendations:\n"
        for rec in self.analysis_results.get('recommendations', []):
            summary_text += f"• {rec}\n"

        return summary_text
