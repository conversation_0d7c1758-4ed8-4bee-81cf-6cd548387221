#!/usr/bin/env python3
"""
Test script to verify the improvements made to the Firewall Social Media Access application.
Tests date validation, PDF file validation, and font loading.
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils import InputValidator, FileHandler
from modern_ui import ModernTheme

def test_date_validation():
    """Test the enhanced date validation."""
    print("🗓️ Testing Date Validation...")
    
    # Valid dates
    valid_dates = [
        "2024-01-15",
        "2023-12-31",
        "2025-06-30"
    ]
    
    # Invalid dates
    invalid_dates = [
        "2024-1-15",      # Wrong format (single digit month)
        "24-01-15",       # Wrong format (2-digit year)
        "2024/01/15",     # Wrong separator
        "2024-13-01",     # Invalid month
        "2024-02-30",     # Invalid day
        "2024-01-32",     # Invalid day
        "abcd-ef-gh",     # Non-numeric
        "2024-01",        # Incomplete
        "2024-01-15-16",  # Too long
        ""                # Empty
    ]
    
    print("  ✅ Testing valid dates:")
    for date_str in valid_dates:
        result = InputValidator.validate_date(date_str)
        print(f"    {date_str}: {'✅ PASS' if result else '❌ FAIL'}")
        assert result, f"Valid date {date_str} should pass validation"
    
    print("  ❌ Testing invalid dates:")
    for date_str in invalid_dates:
        result = InputValidator.validate_date(date_str)
        print(f"    {date_str}: {'✅ PASS' if not result else '❌ FAIL'}")
        assert not result, f"Invalid date {date_str} should fail validation"
    
    print("  ✅ Date validation tests passed!\n")

def test_pdf_validation():
    """Test PDF-only file validation."""
    print("📄 Testing PDF File Validation...")
    
    # Test file extensions
    test_files = [
        ("document.pdf", True),
        ("image.jpg", False),
        ("image.png", False),
        ("document.doc", False),
        ("archive.zip", False),
        ("script.py", False),
        ("data.csv", False),
        ("presentation.ppt", False)
    ]
    
    print("  Testing file extension validation:")
    for filename, should_pass in test_files:
        # Create a temporary file path for testing
        import tempfile
        with tempfile.NamedTemporaryFile(suffix=os.path.splitext(filename)[1], delete=False) as tmp:
            tmp.write(b"Test content")
            tmp_path = tmp.name
        
        try:
            result = FileHandler.validate_file(tmp_path)
            status = "✅ PASS" if (result == should_pass) else "❌ FAIL"
            expected = "should pass" if should_pass else "should fail"
            print(f"    {filename}: {status} ({expected})")
            
            if should_pass:
                assert result, f"PDF file {filename} should pass validation"
            else:
                assert not result, f"Non-PDF file {filename} should fail validation"
        finally:
            # Clean up temporary file
            try:
                os.unlink(tmp_path)
            except:
                pass
    
    print("  ✅ PDF validation tests passed!\n")

def test_font_detection():
    """Test font detection and fallback."""
    print("🔤 Testing Font Detection...")
    
    try:
        import tkinter as tk
        from tkinter import font
        
        # Test font families
        test_fonts = ["Roboto", "Segoe UI", "Arial", "Helvetica"]
        
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        print("  Available fonts:")
        for font_family in test_fonts:
            try:
                test_font = font.Font(family=font_family, size=12)
                actual_family = test_font.actual("family")
                is_available = font_family.lower() in actual_family.lower()
                status = "✅ Available" if is_available else "❌ Not available"
                print(f"    {font_family}: {status} (actual: {actual_family})")
            except Exception as e:
                print(f"    {font_family}: ❌ Error - {e}")
        
        root.destroy()
        print("  ✅ Font detection tests completed!\n")
        
    except ImportError:
        print("  ⚠️ Tkinter not available for font testing\n")

def test_theme_colors():
    """Test theme color definitions."""
    print("🎨 Testing Theme Colors...")
    
    # Test that all theme colors are defined
    required_colors = [
        'PRIMARY', 'PRIMARY_DARK', 'PRIMARY_LIGHT',
        'SECONDARY', 'SECONDARY_LIGHT', 'SECONDARY_DARK',
        'SUCCESS', 'WARNING', 'ERROR',
        'BACKGROUND', 'SURFACE', 'SURFACE_VARIANT',
        'TEXT_PRIMARY', 'TEXT_SECONDARY', 'TEXT_MUTED',
        'BORDER', 'BORDER_FOCUS', 'SHADOW'
    ]
    
    print("  Checking color definitions:")
    for color_name in required_colors:
        if hasattr(ModernTheme, color_name):
            color_value = getattr(ModernTheme, color_name)
            print(f"    {color_name}: ✅ {color_value}")
        else:
            print(f"    {color_name}: ❌ Missing")
            assert False, f"Color {color_name} is not defined"
    
    print("  ✅ Theme color tests passed!\n")

def main():
    """Run all tests."""
    print("🧪 Running Firewall Access App Improvement Tests\n")
    print("=" * 60)
    
    try:
        test_date_validation()
        test_pdf_validation()
        test_font_detection()
        test_theme_colors()
        
        print("🎉 All tests passed successfully!")
        print("\n✨ Improvements verified:")
        print("  • ✅ Strict YYYY-MM-DD date format validation")
        print("  • ✅ PDF-only file upload restriction")
        print("  • ✅ Roboto font preference with fallbacks")
        print("  • ✅ Modern theme color definitions")
        print("  • ✅ Enhanced input validation")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
