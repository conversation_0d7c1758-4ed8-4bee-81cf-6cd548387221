"""
Cybersecurity Awareness Activity Forms Module

Provides form interfaces for creating and editing cybersecurity awareness activities.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Callable, Optional
from datetime import date

from modern_ui import (
    ModernCard, ModernEntry, ModernButton, ModernDateEntry, ModernTheme,
    get_responsive_padding
)
from utils import InputValidator, ValidationError, show_error_message


class AwarenessActivityForm:
    """
    Form for creating and editing cybersecurity awareness activities.
    """
    
    def __init__(self, parent_frame, on_submit_callback: Callable, responsive_manager):
        """
        Initialize the awareness activity form.
        
        Args:
            parent_frame: Parent tkinter frame
            on_submit_callback: Callback function for form submission
            responsive_manager: Responsive layout manager
        """
        self.parent_frame = parent_frame
        self.on_submit_callback = on_submit_callback
        self.responsive_manager = responsive_manager
        
        # Form data
        self.current_id = None
        
        # Create form
        self.create_form()
    
    def create_form(self):
        """Create the awareness activity form."""
        # Ensure parent frame is properly configured
        self.parent_frame.columnconfigure(0, weight=1)
        self.parent_frame.rowconfigure(0, weight=1)

        # Form card
        self.form_card = ModernCard(self.parent_frame, title="📝 Cybersecurity Awareness Activity")
        self.form_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                          padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
                          pady=10)

        # Form content
        form_content = ttk.Frame(self.form_card, style='Surface.TFrame')
        self.form_card.add_content(form_content)

        # Configure grid for responsive layout
        form_content.columnconfigure(0, weight=1)
        form_content.rowconfigure(0, weight=1)

        # Create responsive form fields
        self.create_form_fields(form_content)

    def create_form_fields(self, parent):
        """Create form fields with responsive layout."""
        # Ensure responsive manager is available
        if not hasattr(self, 'responsive_manager') or not self.responsive_manager:
            print("Warning: ResponsiveManager not available, using default layout")
            is_mobile = False
        else:
            # Check if we're on mobile
            is_mobile = self.responsive_manager.current_breakpoint in ['xs', 'sm']

        # Configure grid columns for responsive layout
        if is_mobile:
            parent.columnconfigure(0, weight=1)
        else:
            parent.columnconfigure(0, weight=1)
            parent.columnconfigure(1, weight=1)

        row = 0

        # Title/Subject (always full width)
        self.topic_subject_var = tk.StringVar()
        self.topic_subject_field = ModernEntry(
            parent, "Title/Subject", required=True, textvariable=self.topic_subject_var
        )
        self.topic_subject_field.grid(row=row, column=0, columnspan=2 if not is_mobile else 1,
                                    sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_SM))
        row += 1

        # Date and Number of Participants
        if is_mobile:
            # Mobile: stack vertically
            self.activity_date_var = tk.StringVar()
            self.activity_date_field = ModernDateEntry(
                parent, "Date", required=True, textvariable=self.activity_date_var
            )
            self.activity_date_field.grid(row=row, column=0, sticky=(tk.W, tk.E),
                                        pady=(0, ModernTheme.PADDING_SM))
            row += 1

            self.number_of_pax_var = tk.StringVar()
            self.number_of_pax_field = ModernEntry(
                parent, "Number of Participants", required=True, textvariable=self.number_of_pax_var
            )
            self.number_of_pax_field.grid(row=row, column=0, sticky=(tk.W, tk.E),
                                        pady=(0, ModernTheme.PADDING_SM))
            row += 1
        else:
            # Desktop: side by side
            date_pax_frame = ttk.Frame(parent, style='Surface.TFrame')
            date_pax_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E),
                              pady=(0, ModernTheme.PADDING_SM))
            date_pax_frame.columnconfigure(0, weight=1)
            date_pax_frame.columnconfigure(1, weight=1)

            self.activity_date_var = tk.StringVar()
            self.activity_date_field = ModernDateEntry(
                date_pax_frame, "Date", required=True, textvariable=self.activity_date_var
            )
            self.activity_date_field.grid(row=0, column=0, sticky=(tk.W, tk.E),
                                        padx=(0, ModernTheme.PADDING_SM))

            self.number_of_pax_var = tk.StringVar()
            self.number_of_pax_field = ModernEntry(
                date_pax_frame, "Number of Participants", required=True, textvariable=self.number_of_pax_var
            )
            self.number_of_pax_field.grid(row=0, column=1, sticky=(tk.W, tk.E))
            row += 1

        # Test Results
        if is_mobile:
            # Mobile: stack vertically
            self.pretest_result_var = tk.StringVar()
            self.pretest_result_field = ModernEntry(
                parent, "Pre-test Percentage", textvariable=self.pretest_result_var
            )
            self.pretest_result_field.grid(row=row, column=0, sticky=(tk.W, tk.E),
                                         pady=(0, ModernTheme.PADDING_SM))
            row += 1

            self.posttest_result_var = tk.StringVar()
            self.posttest_result_field = ModernEntry(
                parent, "Post-test Percentage", textvariable=self.posttest_result_var
            )
            self.posttest_result_field.grid(row=row, column=0, sticky=(tk.W, tk.E),
                                          pady=(0, ModernTheme.PADDING_SM))
            row += 1
        else:
            # Desktop: side by side
            test_frame = ttk.Frame(parent, style='Surface.TFrame')
            test_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E),
                          pady=(0, ModernTheme.PADDING_SM))
            test_frame.columnconfigure(0, weight=1)
            test_frame.columnconfigure(1, weight=1)

            self.pretest_result_var = tk.StringVar()
            self.pretest_result_field = ModernEntry(
                test_frame, "Pre-test Percentage", textvariable=self.pretest_result_var
            )
            self.pretest_result_field.grid(row=0, column=0, sticky=(tk.W, tk.E),
                                         padx=(0, ModernTheme.PADDING_SM))

            self.posttest_result_var = tk.StringVar()
            self.posttest_result_field = ModernEntry(
                test_frame, "Post-test Percentage", textvariable=self.posttest_result_var
            )
            self.posttest_result_field.grid(row=0, column=1, sticky=(tk.W, tk.E))
            row += 1

        # Remarks field (always full width)
        self.create_remarks_field(parent, row)
        row += 1

        # Buttons
        self.create_form_buttons(parent, row)

    def create_remarks_field(self, parent, row):
        """Create the remarks text field."""
        remarks_frame = ttk.Frame(parent, style='Surface.TFrame')
        remarks_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E),
                         pady=(0, ModernTheme.PADDING_MD))
        remarks_frame.columnconfigure(0, weight=1)

        remarks_label = ttk.Label(remarks_frame, text="Remarks", style='FieldLabel.TLabel')
        remarks_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # Adjust height based on screen size
        is_mobile = self.responsive_manager.current_breakpoint in ['xs', 'sm']
        text_height = 3 if is_mobile else 4

        self.remarks_text = tk.Text(
            remarks_frame, height=text_height, width=40,
            font=('Roboto', 10),
            bg=ModernTheme.SURFACE,
            fg=ModernTheme.TEXT_PRIMARY,
            relief=tk.SOLID,
            borderwidth=1,
            wrap=tk.WORD
        )
        self.remarks_text.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def create_form_buttons(self, parent, row):
        """Create responsive form buttons."""
        button_frame = ttk.Frame(parent, style='Surface.TFrame')
        button_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E),
                        pady=(ModernTheme.PADDING_MD, 0))

        is_mobile = self.responsive_manager.current_breakpoint in ['xs', 'sm']

        if is_mobile:
            # Mobile: stack buttons vertically
            button_frame.columnconfigure(0, weight=1)

            self.submit_button = ModernButton(
                button_frame, variant="primary", text="Save Activity", command=self.submit_form
            )
            self.submit_button.grid(row=0, column=0, sticky=(tk.W, tk.E),
                                  pady=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(
                button_frame, variant="secondary", text="Clear Form", command=self.clear_form
            )
            self.clear_button.grid(row=1, column=0, sticky=(tk.W, tk.E))
        else:
            # Desktop: side by side buttons
            self.submit_button = ModernButton(
                button_frame, variant="primary", text="Save Activity", command=self.submit_form
            )
            self.submit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(
                button_frame, variant="secondary", text="Clear Form", command=self.clear_form
            )
            self.clear_button.pack(side=tk.LEFT)

    
    def submit_form(self):
        """Validate and submit the form."""
        try:
            # Validate required fields
            topic_subject = self.topic_subject_var.get().strip()
            if not topic_subject:
                raise ValidationError("Title/Subject is required")
            
            activity_date = self.activity_date_var.get().strip()
            if not activity_date:
                raise ValidationError("Date is required")
            
            # Validate number of participants
            number_of_pax_str = self.number_of_pax_var.get().strip()
            if not number_of_pax_str:
                raise ValidationError("Number of Participants is required")
            
            try:
                number_of_pax = int(number_of_pax_str)
                if number_of_pax <= 0:
                    raise ValidationError("Number of Participants must be a positive number")
            except ValueError:
                raise ValidationError("Number of Participants must be a valid number")
            
            # Validate test results (optional)
            pretest_result = None
            pretest_result_str = self.pretest_result_var.get().strip()
            if pretest_result_str:
                try:
                    pretest_result = float(pretest_result_str)
                    if pretest_result < 0 or pretest_result > 100:
                        raise ValidationError("Pre-test Result must be between 0 and 100")
                except ValueError:
                    raise ValidationError("Pre-test Result must be a valid number")
            
            posttest_result = None
            posttest_result_str = self.posttest_result_var.get().strip()
            if posttest_result_str:
                try:
                    posttest_result = float(posttest_result_str)
                    if posttest_result < 0 or posttest_result > 100:
                        raise ValidationError("Post-test Result must be between 0 and 100")
                except ValueError:
                    raise ValidationError("Post-test Result must be a valid number")
            
            # Get remarks
            remarks = self.remarks_text.get("1.0", tk.END).strip()
            
            # Prepare form data
            form_data = {
                'topic_subject': topic_subject,
                'activity_date': activity_date,
                'number_of_pax': number_of_pax,
                'pretest_result': pretest_result,
                'posttest_result': posttest_result,
                'remarks': remarks
            }
            
            # Add ID if editing existing record
            if self.current_id:
                form_data['id'] = self.current_id
            
            # Submit form data
            self.on_submit_callback(form_data)
            
        except ValidationError as e:
            show_error_message("Validation Error", str(e))
    
    def clear_form(self):
        """Clear all form fields."""
        self.current_id = None
        self.topic_subject_var.set('')
        self.activity_date_var.set('')
        self.number_of_pax_var.set('')
        self.pretest_result_var.set('')
        self.posttest_result_var.set('')
        self.remarks_text.delete("1.0", tk.END)
        
        # Update button text
        self.submit_button.configure(text="Save Activity")
    
    def load_record(self, record: Dict):
        """
        Load an existing record into the form for editing.
        
        Args:
            record: Dictionary containing record data
        """
        self.clear_form()
        
        # Set current ID
        self.current_id = record.get('id')
        
        # Load data into form fields
        self.topic_subject_var.set(record.get('topic_subject', ''))
        self.activity_date_var.set(record.get('activity_date', ''))
        self.number_of_pax_var.set(str(record.get('number_of_pax', '')))
        
        if record.get('pretest_result') is not None:
            self.pretest_result_var.set(str(record.get('pretest_result', '')))
        
        if record.get('posttest_result') is not None:
            self.posttest_result_var.set(str(record.get('posttest_result', '')))
        
        if record.get('remarks'):
            self.remarks_text.delete("1.0", tk.END)
            self.remarks_text.insert("1.0", record.get('remarks', ''))
        
        # Update button text
        self.submit_button.configure(text="Update Activity")

    def refresh_layout(self):
        """Refresh the form layout when screen size changes."""
        # Clear the form content and recreate with new responsive layout
        for widget in self.form_card.winfo_children():
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    child.destroy()

        # Recreate form content
        form_content = ttk.Frame(self.form_card, style='Surface.TFrame')
        self.form_card.add_content(form_content)

        # Store current values
        current_values = {
            'topic_subject': self.topic_subject_var.get(),
            'activity_date': self.activity_date_var.get(),
            'number_of_pax': self.number_of_pax_var.get(),
            'pretest_result': self.pretest_result_var.get(),
            'posttest_result': self.posttest_result_var.get(),
            'remarks': self.remarks_text.get("1.0", tk.END) if hasattr(self, 'remarks_text') else ''
        }

        # Recreate form fields with new layout
        self.create_form_fields(form_content)

        # Restore values
        self.topic_subject_var.set(current_values['topic_subject'])
        self.activity_date_var.set(current_values['activity_date'])
        self.number_of_pax_var.set(current_values['number_of_pax'])
        self.pretest_result_var.set(current_values['pretest_result'])
        self.posttest_result_var.set(current_values['posttest_result'])
        if current_values['remarks'].strip():
            self.remarks_text.delete("1.0", tk.END)
            self.remarks_text.insert("1.0", current_values['remarks'])
