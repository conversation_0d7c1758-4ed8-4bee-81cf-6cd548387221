# 🛠️ Python SSL Checker – Desktop Application Module

## 🎯 Objective
Enhance the existing Python desktop app with a new **SSL Checker** module for monitoring web app SSL statuses via URLs, including expiration alerts and secure PDF upload functionality.

---

## ✅ Functional Requirements

### 1. Menu Addition
- Add a new menu item: **"SSL Checker"**

### 2. Input Form
Form should collect:
- **Application Name**
- **IP Address**
- **URL**
- **Remarks**
- **Mode of Verification** (PDF or PNG Upload Only)

### 3. SSL Scan Capability
- Add a button to **scan SSL details** of listed applications via their URL.
- Extract and display:
  - SSL validity
  - Expiration date

### 4. Dynamic Notification
- Display a **real-time notification** if an SSL certificate is already expired.

### 5. Upload Verification
- Allow upload of **PDF or PNG files only** as a verification document.

### 6. Export Options
- **CSV Download**: Export all SSL records.

### 7. Filtering
- Add filtering by **Application Name**.

### 8. Row Color Logic
- Rows with `expiration_status = "yes"`:  
  - **Color**: `#ffc107` (yellow)  
  - **NOT** considered expired
- Rows with actual **expired SSL** and `expiration_status = "no"`:  
  - **Color**: `red`  
  - **Marked as expired**

---

## 🧰 Tech Stack
- **Language**: Python 3.x  
- **GUI Framework**: Tkinter or PyQt  
- **Database**: SQLite  
- **SSL Check**: Python’s `ssl`, `socket`, or third-party libraries like `certifi`, `OpenSSL`

---

## 🛡️ Security & Documentation
- **Comment your code thoroughly**
- Enforce:
  - Input validation
  - File type checking (PDF or PNG only)
  - Secure DB interaction (use parameterized queries)
- Keep code modular and DRY (Don’t Repeat Yourself)