"""
SSL Checker module for monitoring SSL certificate status and expiration.
Provides SSL scanning functionality and certificate validation.
"""

import ssl
import socket
import datetime
from urllib.parse import urlparse
from typing import Dict, <PERSON><PERSON>, Optional
import threading
import time


class <PERSON><PERSON><PERSON><PERSON>:
    """SSL certificate checker and validator."""

    def __init__(self):
        """Initialize SSL checker."""
        self.timeout = 10  # Connection timeout in seconds

    def check_ssl_certificate(self, url: str) -> Dict[str, str]:
        """
        Check SSL certificate for a given URL.

        Args:
            url: URL to check SSL certificate for

        Returns:
            Dictionary containing SSL information
        """
        try:
            # Parse URL to get hostname and port
            parsed_url = urlparse(url if url.startswith(('http://', 'https://')) else f'https://{url}')
            hostname = parsed_url.hostname
            port = parsed_url.port or 443

            if not hostname:
                return {
                    'ssl_valid': 'Invalid',
                    'ssl_expiration_date': '',
                    'error': 'Invalid URL format',
                    'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

            # Create SSL context
            context = ssl.create_default_context()

            # Connect and get certificate
            with socket.create_connection((hostname, port), timeout=self.timeout) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()

                    if cert:
                        # Extract expiration date
                        not_after = cert.get('notAfter')
                        if not_after:
                            # Parse SSL date format: 'MMM DD HH:MM:SS YYYY GMT'
                            expiration_date = datetime.datetime.strptime(not_after, '%b %d %H:%M:%S %Y %Z')
                            expiration_date_str = expiration_date.strftime('%Y-%m-%d')

                            # Check if certificate is valid
                            current_date = datetime.datetime.now()
                            is_valid = current_date < expiration_date

                            return {
                                'ssl_valid': 'Valid' if is_valid else 'Expired',
                                'ssl_expiration_date': expiration_date_str,
                                'error': '',
                                'last_scan_date': current_date.strftime('%Y-%m-%d %H:%M:%S')
                            }
                        else:
                            return {
                                'ssl_valid': 'Invalid',
                                'ssl_expiration_date': '',
                                'error': 'No expiration date found in certificate',
                                'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                    else:
                        return {
                            'ssl_valid': 'Invalid',
                            'ssl_expiration_date': '',
                            'error': 'No certificate found',
                            'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }

        except socket.timeout:
            return {
                'ssl_valid': 'Error',
                'ssl_expiration_date': '',
                'error': 'Connection timeout',
                'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except socket.gaierror:
            return {
                'ssl_valid': 'Error',
                'ssl_expiration_date': '',
                'error': 'DNS resolution failed',
                'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except ssl.SSLError as e:
            return {
                'ssl_valid': 'Error',
                'ssl_expiration_date': '',
                'error': f'SSL Error: {str(e)}',
                'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            return {
                'ssl_valid': 'Error',
                'ssl_expiration_date': '',
                'error': f'Unexpected error: {str(e)}',
                'last_scan_date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def is_certificate_expired(self, expiration_date_str: str) -> bool:
        """
        Check if a certificate is expired based on expiration date string.

        Args:
            expiration_date_str: Expiration date in YYYY-MM-DD format

        Returns:
            True if certificate is expired, False otherwise
        """
        try:
            if not expiration_date_str:
                return False

            expiration_date = datetime.datetime.strptime(expiration_date_str, '%Y-%m-%d')
            current_date = datetime.datetime.now()
            return current_date > expiration_date
        except ValueError:
            return False

    def get_days_until_expiration(self, expiration_date_str: str) -> Optional[int]:
        """
        Get number of days until certificate expiration.

        Args:
            expiration_date_str: Expiration date in YYYY-MM-DD format

        Returns:
            Number of days until expiration, None if invalid date
        """
        try:
            if not expiration_date_str:
                return None

            expiration_date = datetime.datetime.strptime(expiration_date_str, '%Y-%m-%d')
            current_date = datetime.datetime.now()
            delta = expiration_date - current_date
            return delta.days
        except ValueError:
            return None


class SSLValidator:
    """SSL validation utilities for the application."""

    @staticmethod
    def is_ssl_record_expired(record: Dict) -> bool:
        """
        Check if an SSL record is expired based on expiration_status and SSL expiration date.
        Only records with expiration_status="No" and past SSL expiration date are considered expired.

        Args:
            record: Dictionary containing SSL record data

        Returns:
            True if SSL record is expired, False otherwise
        """
        # Safely get expiration_status with None handling
        expiration_status = record.get('expiration_status')
        if expiration_status is None:
            expiration_status = ''
        expiration_status = str(expiration_status).strip().lower()

        # Safely get ssl_expiration_date with None handling
        ssl_expiration_date = record.get('ssl_expiration_date')
        if ssl_expiration_date is None:
            ssl_expiration_date = ''
        ssl_expiration_date = str(ssl_expiration_date).strip()

        # Only check for expiration if expiration_status is "No"
        if expiration_status == 'no' and ssl_expiration_date:
            try:
                expiration_date = datetime.datetime.strptime(ssl_expiration_date, '%Y-%m-%d')
                current_date = datetime.datetime.now()
                return current_date > expiration_date
            except ValueError:
                # Invalid date format, consider it not expired
                return False

        return False

    @staticmethod
    def is_ssl_record_manually_expired(record: Dict) -> bool:
        """
        Check if an SSL record is manually marked as expired (expiration_status="Yes").

        Args:
            record: Dictionary containing SSL record data

        Returns:
            True if SSL record is manually marked as expired, False otherwise
        """
        # Safely get expiration_status with None handling
        expiration_status = record.get('expiration_status')
        if expiration_status is None:
            expiration_status = ''
        expiration_status = str(expiration_status).strip().lower()
        return expiration_status == 'yes'

    @staticmethod
    def get_expired_ssl_records(records: list) -> list:
        """
        Get all expired SSL records from a list of records.

        Args:
            records: List of SSL record dictionaries

        Returns:
            List of expired SSL records
        """
        expired_records = []
        for record in records:
            if SSLValidator.is_ssl_record_expired(record):
                expired_records.append(record)
        return expired_records


class AsyncSSLChecker:
    """Asynchronous SSL checker for scanning multiple URLs."""

    def __init__(self, callback=None):
        """
        Initialize async SSL checker.

        Args:
            callback: Function to call when scan is complete
        """
        self.callback = callback
        self.ssl_checker = SSLChecker()

    def scan_url_async(self, url: str, record_data: Dict):
        """
        Scan URL asynchronously and call callback with results.

        Args:
            url: URL to scan
            record_data: Original record data
        """
        def scan_worker():
            ssl_info = self.ssl_checker.check_ssl_certificate(url)
            if self.callback:
                self.callback(record_data, ssl_info)

        thread = threading.Thread(target=scan_worker, daemon=True)
        thread.start()
