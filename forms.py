"""
Data entry form module for Firewall Social Media Access application.
Handles the creation and management of the data input form with modern UI.
"""

import tkinter as tk
from tkinter import ttk
import os
from datetime import date
from typing import Dict, Callable
from utils import InputValidator, FileHandler, ValidationError, select_file_dialog, show_error_message, show_success_message
from modern_ui import ModernCard, ModernEntry, ModernCombobox, ModernButton, ModernDateEntry, ModernTheme, get_responsive_padding


class AccessRequestForm:
    """Main form for entering access request data with modern UI."""

    def __init__(self, parent: tk.Widget, on_submit_callback: Callable[[Dict], None], responsive_manager=None):
        """
        Initialize the access request form.

        Args:
            parent: Parent widget
            on_submit_callback: Callback function to handle form submission
            responsive_manager: Responsive manager for layout adjustments
        """
        self.parent = parent
        self.on_submit_callback = on_submit_callback
        self.responsive_manager = responsive_manager
        self.current_record_id = None  # For editing existing records
        self.current_breakpoint = 'lg'

        # Form variables
        self.ip_address_var = tk.StringVar()
        self.employee_name_var = tk.StringVar()
        self.section_var = tk.StringVar()
        self.position_var = tk.StringVar()
        self.date_requested_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        self.request_type_var = tk.StringVar(value='Temporary')
        self.is_expired_var = tk.StringVar(value='No')
        self.date_expiration_var = tk.StringVar()
        self.is_ipr_reserved_var = tk.StringVar(value='No')
        self.remarks_var = tk.StringVar()
        self.verification_file_path = ""

        self.create_form()
        self.setup_bindings()

        # Register for responsive updates
        if self.responsive_manager:
            self.responsive_manager.add_resize_callback(self.on_breakpoint_change)

    def create_form(self) -> None:
        """Create the modern form UI elements."""
        # Configure parent grid
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(0, weight=1)

        # Main scrollable frame
        self.main_frame = ttk.Frame(self.parent, style='Modern.TFrame')
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                           padx=get_responsive_padding(self.current_breakpoint),
                           pady=get_responsive_padding(self.current_breakpoint))
        self.main_frame.columnconfigure(0, weight=1)

        # Form card
        self.form_card = ModernCard(self.main_frame, title="Access Request Form")
        self.form_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Form content frame
        self.form_content = ttk.Frame(self.form_card, style='Surface.TFrame')
        self.form_card.add_content(self.form_content)
        self.form_content.columnconfigure(0, weight=1)
        self.form_content.columnconfigure(1, weight=1)

        self.create_form_fields()
        self.create_form_buttons()

    def create_form_fields(self) -> None:
        """Create form fields with modern styling."""
        row = 0

        # IP Address
        self.ip_field = ModernEntry(self.form_content, "IP Address", required=True, textvariable=self.ip_address_var)
        self.ip_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        row += 1

        # Employee Name and Section (responsive layout)
        self.employee_field = ModernEntry(self.form_content, "Employee Name", required=True, textvariable=self.employee_name_var)
        self.section_field = ModernEntry(self.form_content, "Section", required=True, textvariable=self.section_var)

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack vertically
            self.employee_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
            self.section_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
        else:
            # Desktop: side by side
            self.employee_field.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(0, ModernTheme.PADDING_SM))
            self.section_field.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(ModernTheme.PADDING_SM, 0))
            row += 1

        # Position and Date Requested (responsive layout)
        self.position_field = ModernEntry(self.form_content, "Position", required=True, textvariable=self.position_var)
        self.date_requested_field = ModernDateEntry(self.form_content, "Date Requested", required=True, textvariable=self.date_requested_var)

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack vertically
            self.position_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
            self.date_requested_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
        else:
            # Desktop: side by side
            self.position_field.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(0, ModernTheme.PADDING_SM))
            self.date_requested_field.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(ModernTheme.PADDING_SM, 0))
            row += 1

        # Request Type and Is Expired (responsive layout)
        self.request_type_field = ModernCombobox(self.form_content, "Request Type", ['Temporary', 'Permanent'], required=True, textvariable=self.request_type_var)
        self.is_expired_field = ModernCombobox(self.form_content, "Is Expired", ['Yes', 'No'], required=True, textvariable=self.is_expired_var)

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack vertically
            self.request_type_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
            self.is_expired_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
        else:
            # Desktop: side by side
            self.request_type_field.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(0, ModernTheme.PADDING_SM))
            self.is_expired_field.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(ModernTheme.PADDING_SM, 0))
            row += 1

        # Date of Expiration with proper spacing
        expiration_frame = ttk.Frame(self.form_content, style='Surface.TFrame')
        expiration_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        expiration_frame.columnconfigure(0, weight=1)

        self.date_expiration_field = ModernDateEntry(expiration_frame, "Date of Expiration", textvariable=self.date_expiration_var)
        self.date_expiration_field.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Add requirement helper text with proper spacing
        self.expiration_helper = ttk.Label(expiration_frame, text="⚠️ Required for Temporary requests",
                                         style='FieldLabel.TLabel', foreground=ModernTheme.ERROR)
        self.expiration_helper.grid(row=1, column=0, sticky=tk.W, pady=(ModernTheme.PADDING_SM, 0))
        row += 1

        # Is IPv4 Reserved and Remarks (responsive layout)
        self.is_ipr_field = ModernCombobox(self.form_content, "Is IPv4 Reserved", ['Yes', 'No'], required=True, textvariable=self.is_ipr_reserved_var)
        self.remarks_field = ModernEntry(self.form_content, "Remarks", textvariable=self.remarks_var)

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack vertically
            self.is_ipr_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
            self.remarks_field.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
            row += 1
        else:
            # Desktop: side by side
            self.is_ipr_field.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(0, ModernTheme.PADDING_SM))
            self.remarks_field.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD), padx=(ModernTheme.PADDING_SM, 0))
            row += 1

        # File upload section
        self.create_file_upload_section(row)

    def create_file_upload_section(self, row: int) -> None:
        """Create file upload section."""
        # File upload frame
        file_frame = ttk.Frame(self.form_content, style='Surface.TFrame')
        file_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_LG))
        file_frame.columnconfigure(0, weight=1)

        # File label
        ttk.Label(file_frame, text="Mode of Verification (PDF or PNG Upload only)", style='FieldLabel.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, ModernTheme.PADDING_XS))

        # File selection frame
        file_select_frame = ttk.Frame(file_frame, style='Surface.TFrame')
        file_select_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        file_select_frame.columnconfigure(0, weight=1)

        self.file_label = ttk.Label(file_select_frame, text="No PDF or PNG file selected", style='FieldLabel.TLabel')
        self.file_label.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, ModernTheme.PADDING_SM))

        self.browse_button = ModernButton(file_select_frame, variant="secondary", text="📎 Browse", command=self.browse_file)
        self.browse_button.grid(row=0, column=1, padx=(0, ModernTheme.PADDING_SM))

        self.view_file_button = ModernButton(file_select_frame, variant="primary", text="👁️ View", command=self.view_file)
        self.view_file_button.grid(row=0, column=2, padx=(0, ModernTheme.PADDING_SM))
        self.view_file_button.configure(state='disabled')  # Initially disabled

        self.clear_file_button = ModernButton(file_select_frame, variant="error", text="✖ Clear", command=self.clear_file)
        self.clear_file_button.grid(row=0, column=3)

        # Add file format helper
        file_helper = ttk.Label(file_frame, text="📄 Only PDF or PNG files are accepted for security",
                               style='FieldLabel.TLabel', foreground=ModernTheme.TEXT_MUTED)
        file_helper.grid(row=2, column=0, sticky=tk.W, pady=(ModernTheme.PADDING_XS, 0))

    def create_form_buttons(self) -> None:
        """Create form action buttons."""
        # Button frame
        button_frame = ttk.Frame(self.form_content, style='Surface.TFrame')
        button_frame.grid(row=100, column=0, columnspan=2, pady=(ModernTheme.PADDING_LG, 0))

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.submit_button = ModernButton(button_frame, variant="primary", text="Submit", command=self.submit_form)
            self.submit_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(button_frame, variant="secondary", text="Clear", command=self.clear_form)
            self.clear_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.cancel_button = ModernButton(button_frame, variant="error", text="Cancel", command=self.cancel_edit)
            self.cancel_button.pack(fill=tk.X)
        else:
            # Desktop: horizontal layout
            self.submit_button = ModernButton(button_frame, variant="primary", text="Submit", command=self.submit_form)
            self.submit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(button_frame, variant="secondary", text="Clear", command=self.clear_form)
            self.clear_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.cancel_button = ModernButton(button_frame, variant="error", text="Cancel", command=self.cancel_edit)
            self.cancel_button.pack(side=tk.LEFT)

        # Hide cancel button initially
        self.cancel_button.pack_forget()

    def setup_bindings(self) -> None:
        """Set up event bindings for form elements."""
        # Update expiration field visibility based on request type
        self.request_type_var.trace_add('write', self.on_request_type_change)
        self.on_request_type_change()  # Initial setup

    def on_request_type_change(self, *args) -> None:
        """Handle request type change to show/hide expiration date field."""
        if self.request_type_var.get() == 'Temporary':
            self.date_expiration_field.configure_entry(state='normal')
            self.expiration_helper.config(foreground=ModernTheme.ERROR)
        else:
            self.date_expiration_field.configure_entry(state='disabled')
            self.date_expiration_var.set('')
            self.expiration_helper.config(foreground=ModernTheme.TEXT_MUTED)

    def on_breakpoint_change(self, breakpoint: str) -> None:
        """Handle responsive breakpoint changes."""
        if breakpoint != self.current_breakpoint:
            self.current_breakpoint = breakpoint
            # Recreate form with new layout
            self.recreate_form()

    def recreate_form(self) -> None:
        """Recreate form with responsive layout."""
        # Store current values
        current_data = self.get_form_data()
        current_id = self.current_record_id

        # Destroy current form
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()

        # Recreate form
        self.create_form()

        # Restore values
        self.load_form_data(current_data)
        self.current_record_id = current_id

        # Update button state if editing
        if current_id:
            self.submit_button.config(text="Update")
            self.cancel_button.pack(side=tk.LEFT if self.current_breakpoint not in ['xs', 'sm'] else tk.TOP,
                                  fill=tk.X if self.current_breakpoint in ['xs', 'sm'] else None)

    def browse_file(self) -> None:
        """Handle file browse button click."""
        file_path = select_file_dialog("Select PDF Verification File")
        if file_path:
            if FileHandler.validate_file(file_path):
                self.verification_file_path = file_path
                filename = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                self.file_label.config(text=f"📄 {filename}", foreground=ModernTheme.TEXT_PRIMARY)
                self.view_file_button.configure(state='normal')  # Enable view button
            else:
                show_error_message("Invalid File",
                                 "Please select a valid PDF or PNG file under 10MB. Only PDF files are accepted for security reasons.")

    def view_file(self) -> None:
        """Handle view file button click."""
        if self.verification_file_path and os.path.exists(self.verification_file_path):
            if FileHandler.view_file(self.verification_file_path):
                # File opened successfully
                pass
        else:
            show_error_message("File Not Found", "No file selected or file does not exist.")

    def clear_file(self) -> None:
        """Handle clear file button click."""
        self.verification_file_path = ""
        self.file_label.config(text="No PDF or PNG file selected", foreground=ModernTheme.TEXT_MUTED)
        self.view_file_button.configure(state='disabled')  # Disable view button

    def get_form_data(self) -> Dict:
        """
        Get current form data as dictionary.

        Returns:
            Dictionary containing form field values
        """
        return {
            'ip_address': self.ip_field.get(),
            'employee_name': self.employee_field.get(),
            'section': self.section_field.get(),
            'position': self.position_field.get(),
            'date_requested': self.date_requested_field.get(),
            'request_type': self.request_type_field.get(),
            'is_expired': self.is_expired_field.get(),
            'date_expiration': self.date_expiration_field.get() if self.request_type_field.get() == 'Temporary' else None,
            'is_ipr_reserved': self.is_ipr_field.get(),
            'remarks': self.remarks_field.get(),
            'verification_file_path': self.verification_file_path
        }

    def load_form_data(self, data: Dict) -> None:
        """
        Load data into form fields.

        Args:
            data: Dictionary containing form field values
        """
        self.ip_field.set(data.get('ip_address', '') or '')
        self.employee_field.set(data.get('employee_name', '') or '')
        self.section_field.set(data.get('section', '') or '')
        self.position_field.set(data.get('position', '') or '')
        self.date_requested_field.set(data.get('date_requested', '') or '')
        self.request_type_field.set(data.get('request_type', 'Temporary') or 'Temporary')
        self.is_expired_field.set(data.get('is_expired', 'No') or 'No')

        # Handle date_expiration with None safety
        date_expiration = data.get('date_expiration')
        self.date_expiration_field.set(date_expiration if date_expiration is not None else '')

        self.is_ipr_field.set(data.get('is_ipr_reserved', 'No') or 'No')
        self.remarks_field.set(data.get('remarks', '') or '')

        # Handle verification file - handle None values safely
        file_path = data.get('verification_file_path', '')
        file_path_str = str(file_path).strip() if file_path is not None else ''
        if file_path_str:
            self.verification_file_path = file_path_str
            filename = file_path_str.split('/')[-1] if '/' in file_path_str else file_path_str.split('\\')[-1]
            self.file_label.config(text=f"📄 {filename}", foreground=ModernTheme.TEXT_PRIMARY)
            if hasattr(self, 'view_file_button'):
                self.view_file_button.configure(state='normal')  # Enable view button
        else:
            self.verification_file_path = ""
            self.file_label.config(text="No PDF or PNG file selected", foreground=ModernTheme.TEXT_MUTED)
            if hasattr(self, 'view_file_button'):
                self.view_file_button.configure(state='disabled')  # Disable view button

    def submit_form(self) -> None:
        """Handle form submission."""
        try:
            # Get and validate form data
            form_data = self.get_form_data()
            validated_data = InputValidator.validate_form_data(form_data)

            # Handle file upload if present
            if self.verification_file_path:
                try:
                    new_file_path = FileHandler.copy_verification_file(
                        self.verification_file_path,
                        validated_data['employee_name']
                    )
                    validated_data['verification_file_path'] = new_file_path
                except Exception as e:
                    show_error_message("File Upload Error", f"Failed to upload file: {e}")
                    return

            # Add record ID for updates
            if self.current_record_id:
                validated_data['id'] = self.current_record_id

            # Call the submission callback
            self.on_submit_callback(validated_data)

            # Note: Success message and form clearing is now handled by the main application

        except ValidationError as e:
            show_error_message("Validation Error", str(e))
        except Exception as e:
            show_error_message("Error", f"An error occurred: {e}")

    def clear_form(self) -> None:
        """Clear all form fields."""
        self.current_record_id = None
        self.ip_field.set('')
        self.employee_field.set('')
        self.section_field.set('')
        self.position_field.set('')
        self.date_requested_field.set(date.today().strftime('%Y-%m-%d'))
        self.request_type_field.set('Temporary')
        self.is_expired_field.set('No')
        self.date_expiration_field.set('')
        self.is_ipr_field.set('No')
        self.remarks_field.set('')
        self.verification_file_path = ""
        self.file_label.config(text="No PDF or PNG file selected", foreground=ModernTheme.TEXT_MUTED)
        if hasattr(self, 'view_file_button'):
            self.view_file_button.configure(state='disabled')  # Disable view button

        # Hide cancel button and change submit button text
        self.cancel_button.pack_forget()
        self.submit_button.config(text="Submit")

    def load_record(self, record: Dict) -> None:
        """
        Load a record into the form for editing.

        Args:
            record: Dictionary containing record data
        """
        self.current_record_id = record['id']
        self.load_form_data(record)

        # Show cancel button and change submit button text
        self.submit_button.config(text="Update")
        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: show cancel button below
            self.cancel_button.pack(fill=tk.X)
        else:
            # Desktop: show cancel button to the right
            self.cancel_button.pack(side=tk.LEFT)

    def cancel_edit(self) -> None:
        """Cancel editing and clear the form."""
        self.clear_form()
