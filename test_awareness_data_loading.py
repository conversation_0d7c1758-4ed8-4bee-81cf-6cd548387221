#!/usr/bin/env python3
"""
Test script to verify awareness data loading and display
"""

import sys
import os

# Add current directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager

def test_awareness_data():
    """Test awareness data loading from database."""
    
    print("🔍 Testing Awareness Data Loading...")
    
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Ensure awareness table exists
        db_manager.create_awareness_table()
        print("✅ Awareness table created/verified")
        
        # Get all awareness activities
        activities = db_manager.get_all_awareness_activities()
        print(f"📊 Found {len(activities)} awareness activities in database")
        
        if activities:
            print("\n📋 Awareness Activities:")
            for i, activity in enumerate(activities, 1):
                print(f"  {i}. ID: {activity.get('id')}")
                print(f"     Title/Subject: {activity.get('topic_subject', 'N/A')}")
                print(f"     Date: {activity.get('activity_date', 'N/A')}")
                print(f"     Participants: {activity.get('number_of_pax', 'N/A')}")
                print(f"     Pre-test: {activity.get('pretest_result', 'N/A')}")
                print(f"     Post-test: {activity.get('posttest_result', 'N/A')}")
                print(f"     Remarks: {activity.get('remarks', 'N/A')[:50]}...")
                print()
        else:
            print("❌ No awareness activities found in database")
            print("💡 Try adding some sample data using test_awareness_data.py")
        
        return activities
        
    except Exception as e:
        print(f"❌ Error testing awareness data: {e}")
        import traceback
        traceback.print_exc()
        return []

def add_sample_data_if_empty():
    """Add sample data if database is empty."""
    
    try:
        db_manager = DatabaseManager()
        activities = db_manager.get_all_awareness_activities()
        
        if not activities:
            print("📝 Adding sample awareness activities...")
            
            sample_activities = [
                {
                    'topic_subject': 'Phishing Awareness Training',
                    'activity_date': '2024-01-15',
                    'number_of_pax': 25,
                    'pretest_result': 65.5,
                    'posttest_result': 89.2,
                    'remarks': 'Excellent participation. Significant improvement in identifying phishing emails.'
                },
                {
                    'topic_subject': 'Password Security Best Practices',
                    'activity_date': '2024-01-22',
                    'number_of_pax': 18,
                    'pretest_result': 72.1,
                    'posttest_result': 91.7,
                    'remarks': 'Good understanding of password complexity requirements.'
                },
                {
                    'topic_subject': 'Social Engineering Defense',
                    'activity_date': '2024-02-01',
                    'number_of_pax': 30,
                    'pretest_result': 58.3,
                    'posttest_result': 85.6,
                    'remarks': 'Interactive scenarios were very effective in demonstrating social engineering tactics.'
                }
            ]
            
            for activity in sample_activities:
                activity_id = db_manager.add_awareness_activity(activity)
                print(f"  ✅ Added: {activity['topic_subject']} (ID: {activity_id})")
            
            print(f"📊 Added {len(sample_activities)} sample activities")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Awareness Data Test")
    print("=" * 50)
    
    # Test current data
    activities = test_awareness_data()
    
    # Add sample data if empty
    if not activities:
        if add_sample_data_if_empty():
            print("\n🔄 Re-testing after adding sample data...")
            test_awareness_data()
    
    print("\n✅ Test completed!")
