#!/usr/bin/env python3
"""
Demo script showing the key improvements made to the Firewall Social Media Access application.
"""

import tkinter as tk
from tkinter import ttk
from modern_ui import ModernDateEntry, ModernTheme, StyleManager

def create_demo_window():
    """Create a demo window showing the improvements."""
    root = tk.Tk()
    root.title("🛡️ Firewall Access App - Improvements Demo")
    root.geometry("800x600")
    
    # Apply modern styling
    style_manager = StyleManager(root)
    
    # Main frame
    main_frame = ttk.Frame(root, style='Modern.TFrame')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Title
    title_label = ttk.Label(main_frame, text="🎉 Key Improvements Demonstrated", 
                           style='AppTitle.TLabel')
    title_label.pack(pady=(0, 20))
    
    # Improvement 1: Date Entry with Auto-formatting
    improvement1_frame = ttk.LabelFrame(main_frame, text="1. Enhanced Date Entry with Auto-formatting", 
                                       padding="15")
    improvement1_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(improvement1_frame, text="✨ Try typing a date (e.g., 20240115):", 
             style='FieldLabel.TLabel').pack(anchor=tk.W)
    
    date_var = tk.StringVar()
    date_entry = ModernDateEntry(improvement1_frame, "Date with Auto-formatting", 
                                required=True, textvariable=date_var)
    date_entry.pack(fill=tk.X, pady=(5, 0))
    
    # Improvement 2: Font Display
    improvement2_frame = ttk.LabelFrame(main_frame, text="2. Roboto Font with Fallbacks", 
                                       padding="15")
    improvement2_frame.pack(fill=tk.X, pady=(0, 15))
    
    font_info = ttk.Label(improvement2_frame, 
                         text="🔤 This text uses Roboto font (or best available fallback)\n"
                              "Font hierarchy: Roboto → Segoe UI → Arial → Helvetica → sans-serif", 
                         style='FieldLabel.TLabel')
    font_info.pack(anchor=tk.W)
    
    # Improvement 3: PDF-only File Selection
    improvement3_frame = ttk.LabelFrame(main_frame, text="3. PDF-only File Upload", 
                                       padding="15")
    improvement3_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(improvement3_frame, text="📄 File selection now restricted to PDF files only for security", 
             style='FieldLabel.TLabel').pack(anchor=tk.W)
    
    file_frame = ttk.Frame(improvement3_frame)
    file_frame.pack(fill=tk.X, pady=(5, 0))
    
    file_label = ttk.Label(file_frame, text="No PDF file selected", style='FieldLabel.TLabel')
    file_label.pack(side=tk.LEFT)
    
    def browse_pdf():
        from utils import select_file_dialog, FileHandler
        file_path = select_file_dialog("Select PDF File")
        if file_path:
            if FileHandler.validate_file(file_path):
                filename = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                file_label.config(text=f"📄 {filename}")
            else:
                file_label.config(text="❌ Invalid file (PDF only)")
    
    browse_btn = ttk.Button(file_frame, text="Browse PDF", command=browse_pdf)
    browse_btn.pack(side=tk.RIGHT)
    
    # Improvement 4: Better Spacing and Layout
    improvement4_frame = ttk.LabelFrame(main_frame, text="4. Improved Spacing and Layout", 
                                       padding="15")
    improvement4_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(improvement4_frame, 
             text="📐 Fixed text overlap issues with proper spacing\n"
                  "🎨 Modern card-based layout with consistent padding\n"
                  "📱 Responsive design that adapts to screen size", 
             style='FieldLabel.TLabel').pack(anchor=tk.W)
    
    # Improvement 5: Enhanced Validation
    improvement5_frame = ttk.LabelFrame(main_frame, text="5. Strict Date Format Validation", 
                                       padding="15")
    improvement5_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(improvement5_frame, 
             text="✅ Enforces exact YYYY-MM-DD format\n"
                  "❌ Rejects formats like 2024-1-15 or 24-01-15\n"
                  "🔍 Validates actual date existence (no Feb 30th)", 
             style='FieldLabel.TLabel').pack(anchor=tk.W)
    
    # Test validation button
    def test_validation():
        test_dates = ["2024-01-15", "2024-1-15", "24-01-15", "2024-02-30"]
        from utils import InputValidator
        
        result_text = "Validation Results:\n"
        for test_date in test_dates:
            is_valid = InputValidator.validate_date(test_date)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            result_text += f"  {test_date}: {status}\n"
        
        # Show results in a popup
        result_window = tk.Toplevel(root)
        result_window.title("Date Validation Test")
        result_window.geometry("300x200")
        
        ttk.Label(result_window, text=result_text, style='FieldLabel.TLabel').pack(padx=20, pady=20)
        ttk.Button(result_window, text="Close", command=result_window.destroy).pack(pady=10)
    
    test_btn = ttk.Button(improvement5_frame, text="Test Date Validation", command=test_validation)
    test_btn.pack(pady=(10, 0))
    
    # Close button
    close_btn = ttk.Button(main_frame, text="Close Demo", command=root.destroy)
    close_btn.pack(pady=20)
    
    return root

def main():
    """Run the demo."""
    print("🚀 Starting Firewall Access App Improvements Demo...")
    
    try:
        demo_window = create_demo_window()
        demo_window.mainloop()
        print("✅ Demo completed successfully!")
    except Exception as e:
        print(f"❌ Demo error: {e}")

if __name__ == "__main__":
    main()
