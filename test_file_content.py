#!/usr/bin/env python3
"""
Test script to verify the main.py file content
"""

def check_file_content():
    """Check if the main.py file has been updated correctly."""
    try:
        with open('main.py', 'r') as f:
            lines = f.readlines()
        
        print(f"Total lines in main.py: {len(lines)}")
        
        # Check line 1136 (index 1135)
        if len(lines) > 1135:
            print(f"Line 1136: {lines[1135].strip()}")
        else:
            print("Line 1136 does not exist")
        
        # Search for update_breakpoint
        update_breakpoint_lines = []
        for i, line in enumerate(lines, 1):
            if 'update_breakpoint' in line:
                update_breakpoint_lines.append((i, line.strip()))
        
        if update_breakpoint_lines:
            print(f"\nFound 'update_breakpoint' in {len(update_breakpoint_lines)} lines:")
            for line_num, line_content in update_breakpoint_lines:
                print(f"  Line {line_num}: {line_content}")
        else:
            print("\nNo 'update_breakpoint' found in file")
        
        # Search for on_window_resize method
        on_window_resize_lines = []
        for i, line in enumerate(lines, 1):
            if 'def on_window_resize' in line:
                on_window_resize_lines.append((i, line.strip()))
        
        if on_window_resize_lines:
            print(f"\nFound 'def on_window_resize' in {len(on_window_resize_lines)} lines:")
            for line_num, line_content in on_window_resize_lines:
                print(f"  Line {line_num}: {line_content}")
        else:
            print("\nNo 'def on_window_resize' found in file")
        
        # Check for test method
        test_method_lines = []
        for i, line in enumerate(lines, 1):
            if 'def test_responsive_fix' in line:
                test_method_lines.append((i, line.strip()))
        
        if test_method_lines:
            print(f"\nFound 'def test_responsive_fix' in {len(test_method_lines)} lines:")
            for line_num, line_content in test_method_lines:
                print(f"  Line {line_num}: {line_content}")
        else:
            print("\nNo 'def test_responsive_fix' found in file")
            
    except Exception as e:
        print(f"Error reading file: {e}")

if __name__ == "__main__":
    check_file_content()
