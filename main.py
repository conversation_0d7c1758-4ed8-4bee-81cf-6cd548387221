"""
Main application module for Firewall Social Media Access application.
Entry point and main application class with modern responsive GUI management.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
from typing import Dict

# Import application modules
from database import DatabaseManager
from forms import AccessRequestForm
from table_view import AccessRequestTable
from ssl_forms import SSLRecordForm
from ssl_table_view import SSLRecordTable
from simple_threat_gui import SimpleThreatInterface
from utils import (
    FileHandler,
    show_error_message,
    show_success_message,
    save_file_dialog,
    show_confirmation_dialog,
)
from modern_ui import (
    StyleManager,
    ResponsiveManager,
    ModernCard,
    ModernEntry,
    ModernDateEntry,
    ModernButton,
    ModernTheme,
    get_responsive_padding,
    ExpirationValidator,
)


class FirewallAccessApp:
    """Main application class for CSO tools with modern UI."""

    def __init__(self):
        """Initialize the main application with modern responsive design."""
        self.root = tk.Tk()
        self.root.title("🛡️ CSO Tools")
        self.root.geometry("1400x900")
        self.root.minsize(800, 600)

        # Initialize modern UI components
        self.style_manager = StyleManager(self.root)
        self.responsive_manager = ResponsiveManager(self.root)

        # Initialize database
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.create_awareness_table()  # Create awareness activities table
        except Exception as e:
            messagebox.showerror(
                "Database Error", f"Failed to initialize database: {e}"
            )
            sys.exit(1)

        # Initialize UI components
        self.setup_ui()
        self.load_data()
        self.load_ssl_data()
        self.load_awareness_data()

        # Configure window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Set window icon and additional properties
        try:
            self.root.iconname("Firewall Manager")
        except:
            pass  # Icon setting might fail on some systems

    def setup_ui(self) -> None:
        """Set up the modern responsive user interface."""
        # Configure main window grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)

        # Create menu bar
        self.create_menu()

        # Create header
        self.create_header()

        # Create main content area
        self.create_main_content()

        # Create status bar
        self.create_status_bar()

    def create_header(self) -> None:
        """Create modern header with title."""
        header_frame = ttk.Frame(self.root, style="Modern.TFrame")
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=20, pady=(20, 10))
        header_frame.columnconfigure(0, weight=1)

        # App title
        title_label = ttk.Label(
            header_frame, text="🛡️ CSO Tools", style="AppTitle.TLabel"
        )
        title_label.grid(row=0, column=0, sticky=tk.W)

        # Subtitle
        subtitle_label = ttk.Label(
            header_frame,
            text="All-in-one tool of the Cyber Security Officer",
            style="FieldLabel.TLabel",
        )
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

    def create_menu(self) -> None:
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Request", command=self.new_request)
        file_menu.add_separator()
        file_menu.add_command(label="Export to CSV", command=self.export_csv)
        file_menu.add_command(label="Export to TXT", command=self.export_txt)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)

        # Firewall menu (as specified in requirements)
        firewall_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Firewall Social Media Access", menu=firewall_menu)
        firewall_menu.add_command(label="Add New Request", command=self.new_request)
        firewall_menu.add_command(
            label="View All Requests", command=self.view_all_requests
        )
        firewall_menu.add_separator()
        firewall_menu.add_command(
            label="Filter Requests", command=self.show_filter_dialog
        )
        firewall_menu.add_command(label="Clear Filters", command=self.clear_filters)
        firewall_menu.add_separator()
        firewall_menu.add_command(
            label="Check Expired Records", command=self.check_expired_records
        )

        # SSL Checker menu
        ssl_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="SSL Checker", menu=ssl_menu)
        ssl_menu.add_command(label="Add SSL Record", command=self.new_ssl_record)
        ssl_menu.add_command(label="View SSL Records", command=self.view_ssl_records)
        ssl_menu.add_separator()
        ssl_menu.add_command(
            label="Export SSL Records to CSV", command=self.export_ssl_csv
        )
        ssl_menu.add_command(
            label="Export Scan History to CSV", command=self.export_ssl_scan_history
        )
        ssl_menu.add_separator()
        ssl_menu.add_command(
            label="View Scan Statistics", command=self.show_ssl_scan_statistics
        )

        # Firewall Threat Log Analyzer menu
        threat_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Firewall Threat Log Analyzer", menu=threat_menu)
        threat_menu.add_command(
            label="Open Threat Analyzer", command=self.open_threat_analyzer
        )
        threat_menu.add_separator()
        threat_menu.add_command(
            label="About Threat Analyzer", command=self.show_threat_analyzer_about
        )

        # Cybersecurity Awareness menu
        awareness_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Cybersecurity Awareness", menu=awareness_menu)
        awareness_menu.add_command(label="Add New Activity", command=self.new_awareness_activity)
        awareness_menu.add_command(label="View All Activities", command=self.view_awareness_activities)
        awareness_menu.add_separator()
        awareness_menu.add_command(label="Export Activities to CSV", command=self.export_awareness_csv)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(
            label="Keyboard Shortcuts", command=self.show_keyboard_shortcuts
        )
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)

    def create_main_content(self) -> None:
        """Create the modern main content area with responsive tabs."""
        # Create notebook for tabs with modern styling
        self.notebook = ttk.Notebook(self.root, style="Modern.TNotebook")
        self.notebook.grid(
            row=1,
            column=0,
            sticky=(tk.W, tk.E, tk.N, tk.S),
            padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
            pady=5,
        )

        # Create form tab
        self.form_frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(self.form_frame, text="📝 Add/Edit Request")

        # Create table tab
        self.table_frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(self.table_frame, text="🔍 View Requests")

        # Create SSL form tab
        self.ssl_form_frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(self.ssl_form_frame, text="🔒 Add/Edit SSL Record")

        # Create SSL table tab
        self.ssl_table_frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(self.ssl_table_frame, text="🔍 View SSL Records")

        # Create Cybersecurity Awareness form tab
        self.cybersecurity_awareness_frame = ttk.Frame(
            self.notebook, style="Modern.TFrame"
        )
        # Configure responsive grid for awareness form frame
        self.cybersecurity_awareness_frame.columnconfigure(0, weight=1)
        self.cybersecurity_awareness_frame.rowconfigure(0, weight=1)
        self.notebook.add(
            self.cybersecurity_awareness_frame, text="📅 Add/Edit Awareness Activity"
        )

        # Create Cybersecurity Awareness table tab
        self.cybersecurity_awareness_table_frame = ttk.Frame(
            self.notebook, style="Modern.TFrame"
        )
        # Configure responsive grid for awareness table frame
        self.cybersecurity_awareness_table_frame.columnconfigure(0, weight=1)
        self.cybersecurity_awareness_table_frame.rowconfigure(0, weight=1)
        self.notebook.add(
            self.cybersecurity_awareness_table_frame,
            text="🔍 View Awareness Activities",
        )

        # Create Threat Analyzer tab
        self.threat_analyzer_frame = ttk.Frame(self.notebook, style="Modern.TFrame")
        self.notebook.add(self.threat_analyzer_frame, text="🛡️ Threat Analyzer")

        # Initialize form with responsive manager
        self.form = AccessRequestForm(
            self.form_frame, self.on_form_submit, self.responsive_manager
        )

        # Initialize table with responsive manager
        self.table = AccessRequestTable(
            self.table_frame,
            self.on_edit_request,
            self.on_delete_request,
            self.responsive_manager,
        )

        # Initialize SSL form with responsive manager
        self.ssl_form = SSLRecordForm(
            self.ssl_form_frame, self.on_ssl_form_submit, self.responsive_manager
        )

        # Initialize SSL table with responsive manager
        self.ssl_table = SSLRecordTable(
            self.ssl_table_frame,
            self.on_edit_ssl_record,
            self.on_delete_ssl_record,
            self.on_update_ssl_record,
            self.responsive_manager,
        )

        # Initialize Cybersecurity Awareness form
        from awareness_forms import AwarenessActivityForm
        self.awareness_form = AwarenessActivityForm(
            self.cybersecurity_awareness_frame, self.on_awareness_form_submit, self.responsive_manager
        )

        # Initialize Cybersecurity Awareness table
        from awareness_table_view import AwarenessActivityTable
        self.awareness_table = AwarenessActivityTable(
            self.cybersecurity_awareness_table_frame,
            self.on_edit_awareness_activity,
            self.on_delete_awareness_activity,
            self.responsive_manager
        )

        # Initialize Simple Threat Analyzer interface
        self.threat_analyzer = SimpleThreatInterface(
            self.threat_analyzer_frame, self.responsive_manager
        )

        # Override table refresh methods
        self.table.refresh_table = self.load_data
        self.ssl_table.refresh_table = self.load_ssl_data
        self.awareness_table.refresh_table = self.load_awareness_data

        # Create filter frame in table tab
        self.create_filter_frame()

        # Create SSL filter frame in SSL table tab
        self.create_ssl_filter_frame()

        # Register for responsive updates
        self.responsive_manager.add_resize_callback(self.on_breakpoint_change)

    def on_breakpoint_change(self, breakpoint: str) -> None:
        """Handle responsive breakpoint changes for main content."""
        # Update main content padding
        current_padding = get_responsive_padding(breakpoint)
        self.notebook.grid_configure(padx=current_padding)

        # Refresh all responsive components when breakpoint changes
        self.refresh_all_responsive_components()

    def create_filter_frame(self) -> None:
        """Create the modern filter frame in the table tab."""
        # Filter card
        filter_card = ModernCard(self.table_frame, title="🔍 Filters")
        filter_card.grid(
            row=2,
            column=0,
            sticky=(tk.W, tk.E),
            padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
            pady=(0, 10),
        )

        # Filter content frame
        filter_content = ttk.Frame(filter_card, style="Surface.TFrame")
        filter_card.add_content(filter_content)
        filter_content.columnconfigure(0, weight=1)
        filter_content.columnconfigure(1, weight=1)
        filter_content.columnconfigure(2, weight=1)

        # Filter variables
        self.filter_date_var = tk.StringVar()
        self.filter_ip_var = tk.StringVar()
        self.filter_name_var = tk.StringVar()

        # Create filter fields
        self.date_filter = ModernDateEntry(
            filter_content, "Filter by Date", textvariable=self.filter_date_var
        )
        self.ip_filter = ModernEntry(
            filter_content, "IP Address", textvariable=self.filter_ip_var
        )
        self.name_filter = ModernEntry(
            filter_content, "Employee Name", textvariable=self.filter_name_var
        )

        # Responsive layout for filters
        if self.responsive_manager.current_breakpoint in ["xs", "sm"]:
            # Mobile: stack vertically
            self.date_filter.grid(
                row=0,
                column=0,
                columnspan=3,
                sticky=(tk.W, tk.E),
                pady=(0, ModernTheme.PADDING_SM),
            )
            self.ip_filter.grid(
                row=1,
                column=0,
                columnspan=3,
                sticky=(tk.W, tk.E),
                pady=(0, ModernTheme.PADDING_SM),
            )
            self.name_filter.grid(
                row=2,
                column=0,
                columnspan=3,
                sticky=(tk.W, tk.E),
                pady=(0, ModernTheme.PADDING_MD),
            )
            button_row = 3
        else:
            # Desktop: side by side
            self.date_filter.grid(
                row=0, column=0, sticky=(tk.W, tk.E), padx=(0, ModernTheme.PADDING_SM)
            )
            self.ip_filter.grid(
                row=0,
                column=1,
                sticky=(tk.W, tk.E),
                padx=(ModernTheme.PADDING_SM, ModernTheme.PADDING_SM),
            )
            self.name_filter.grid(
                row=0, column=2, sticky=(tk.W, tk.E), padx=(ModernTheme.PADDING_SM, 0)
            )
            button_row = 1

        # Filter buttons
        button_frame = ttk.Frame(filter_content, style="Surface.TFrame")
        button_frame.grid(
            row=button_row, column=0, columnspan=3, pady=(ModernTheme.PADDING_MD, 0)
        )

        if self.responsive_manager.current_breakpoint in ["xs", "sm"]:
            # Mobile: full width buttons
            apply_btn = ModernButton(
                button_frame,
                variant="primary",
                text="Apply Filters",
                command=self.apply_filters,
            )
            apply_btn.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            clear_btn = ModernButton(
                button_frame,
                variant="secondary",
                text="Clear Filters",
                command=self.clear_filters,
            )
            clear_btn.pack(fill=tk.X)
        else:
            # Desktop: side by side buttons
            apply_btn = ModernButton(
                button_frame,
                variant="primary",
                text="Apply Filters",
                command=self.apply_filters,
            )
            apply_btn.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            clear_btn = ModernButton(
                button_frame,
                variant="secondary",
                text="Clear Filters",
                command=self.clear_filters,
            )
            clear_btn.pack(side=tk.LEFT)

    def create_ssl_filter_frame(self) -> None:
        """Create the modern SSL filter frame in the SSL table tab."""
        # SSL Filter card
        ssl_filter_card = ModernCard(self.ssl_table_frame, title="🔍 SSL Filters")
        ssl_filter_card.grid(
            row=2,
            column=0,
            sticky=(tk.W, tk.E),
            padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
            pady=(0, 10),
        )

        # SSL Filter content frame
        ssl_filter_content = ttk.Frame(ssl_filter_card, style="Surface.TFrame")
        ssl_filter_card.add_content(ssl_filter_content)
        ssl_filter_content.columnconfigure(0, weight=1)

        # SSL Filter variables
        self.ssl_filter_app_name_var = tk.StringVar()

        # Create SSL filter fields
        self.ssl_app_name_filter = ModernEntry(
            ssl_filter_content,
            "Application Name",
            textvariable=self.ssl_filter_app_name_var,
        )
        self.ssl_app_name_filter.grid(
            row=0, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD)
        )

        # SSL Filter buttons
        ssl_button_frame = ttk.Frame(ssl_filter_content, style="Surface.TFrame")
        ssl_button_frame.grid(row=1, column=0, pady=(ModernTheme.PADDING_MD, 0))

        if self.responsive_manager.current_breakpoint in ["xs", "sm"]:
            # Mobile: full width buttons
            ssl_apply_btn = ModernButton(
                ssl_button_frame,
                variant="primary",
                text="Apply SSL Filters",
                command=self.apply_ssl_filters,
            )
            ssl_apply_btn.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            ssl_clear_btn = ModernButton(
                ssl_button_frame,
                variant="secondary",
                text="Clear SSL Filters",
                command=self.clear_ssl_filters,
            )
            ssl_clear_btn.pack(fill=tk.X)
        else:
            # Desktop: side by side buttons
            ssl_apply_btn = ModernButton(
                ssl_button_frame,
                variant="primary",
                text="Apply SSL Filters",
                command=self.apply_ssl_filters,
            )
            ssl_apply_btn.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            ssl_clear_btn = ModernButton(
                ssl_button_frame,
                variant="secondary",
                text="Clear SSL Filters",
                command=self.clear_ssl_filters,
            )
            ssl_clear_btn.pack(side=tk.LEFT)

    def create_status_bar(self) -> None:
        """Create the status bar."""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")

        status_bar = ttk.Label(
            self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W
        )
        status_bar.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)

        # Keyboard shortcuts
        # Add/Edit Request (Social Media Access)
        self.root.bind("<Control-n>", lambda e: self.new_request())
        # View All Requests (Social Media Access)
        self.root.bind("<Control-m>", lambda e: self.view_all_requests())
        # Add/Edit SSL Record
        self.root.bind("<Control-s>", lambda e: self.new_ssl_record())
        # View All SSL Records
        self.root.bind("<Control-l>", lambda e: self.view_ssl_records())
        # Add/Edit Awareness Activity
        self.root.bind("<Control-a>", lambda e: self.new_awareness_activity())
        # View Awareness Activities
        self.root.bind("<Control-Shift-A>", lambda e: self.view_awareness_activities())
        # Threat Analyzer
        self.root.bind("<Control-g>", lambda e: self.open_threat_analyzer())
        # self.root.bind("<Control-e>", lambda e: self.export_csv())
        # Refresh Social Media Access Requests
        self.root.bind("<Control-r>", lambda e: self.load_data())
        self.root.bind("<F5>", lambda e: self.load_data())
        # self.root.bind(
        #     "<Control-h>", lambda e: self.export_ssl_scan_history()
        # )  # Ctrl+H for scan History
        # self.root.bind(
        #     "<Control-Shift-S>", lambda e: self.show_ssl_scan_statistics()
        # )  # Ctrl+Shift+S for Statistics

    def load_data(self) -> None:
        """Load data from database and refresh the table."""
        try:
            records = self.db_manager.get_all_requests()
            self.table.load_data(records)

            # Check for expired records and update status
            expired_count = len(ExpirationValidator.get_expired_records(records))
            if expired_count > 0:
                self.status_var.set(
                    f"📊 Loaded {len(records)} records (⚠️ {expired_count} expired)"
                )
            else:
                self.status_var.set(f"📊 Loaded {len(records)} records")
        except Exception as e:
            show_error_message("Database Error", f"Failed to load data: {e}")
            self.status_var.set("❌ Error loading data")

    def on_form_submit(self, form_data: Dict) -> None:
        """
        Handle form submission.

        Args:
            form_data: Validated form data
        """
        try:
            if form_data.get("id"):
                # Update existing record
                success = self.db_manager.update_request(form_data["id"], form_data)
                if success:
                    self.load_data()
                    self.form.clear_form()  # Clear form after successful update
                    show_success_message(
                        "Success", "Access request updated successfully!"
                    )
                    self.status_var.set("✅ Record updated successfully")
                else:
                    show_error_message("Update Error", "Failed to update record")
            else:
                # Add new record
                record_id = self.db_manager.add_request(form_data)
                self.load_data()
                self.form.clear_form()  # Clear form after successful addition
                show_success_message(
                    "Success",
                    f"New access request added successfully with ID: {record_id}!",
                )
                self.status_var.set(f"✅ New record added with ID: {record_id}")

                # Show expiration notification for temporary requests
                if form_data["request_type"] == "Temporary" and form_data.get(
                    "date_expiration"
                ):
                    messagebox.showinfo(
                        "⏰ Expiration Notice",
                        f"This temporary access will expire on: {form_data['date_expiration']}",
                    )

        except Exception as e:
            show_error_message("Database Error", f"Failed to save record: {e}")

    def on_edit_request(self, record: Dict) -> None:
        """
        Handle edit request from table.

        Args:
            record: Record data to edit
        """
        # Switch to form tab and load record
        self.notebook.select(0)  # Select first tab (form)
        self.form.load_record(record)
        self.status_var.set(
            f"✏️ Editing record for {record.get('employee_name', 'Unknown')}"
        )

    def on_delete_request(self, record_id: int) -> None:
        """
        Handle delete request from table.

        Args:
            record_id: ID of record to delete
        """
        try:
            success = self.db_manager.delete_request(record_id)
            if success:
                self.load_data()
                self.status_var.set("🗑️ Record deleted successfully")
            else:
                show_error_message("Delete Error", "Failed to delete record")
        except Exception as e:
            show_error_message("Database Error", f"Failed to delete record: {e}")

    def apply_filters(self) -> None:
        """Apply filters to the table data."""
        filters = {}

        # Handle None values safely in filters
        date_filter = self.date_filter.get()
        date_filter_str = str(date_filter).strip() if date_filter is not None else ""
        if date_filter_str:
            filters["date_requested"] = date_filter_str

        ip_filter = self.ip_filter.get()
        ip_filter_str = str(ip_filter).strip() if ip_filter is not None else ""
        if ip_filter_str:
            filters["ip_address"] = ip_filter_str

        name_filter = self.name_filter.get()
        name_filter_str = str(name_filter).strip() if name_filter is not None else ""
        if name_filter_str:
            filters["employee_name"] = name_filter_str

        if filters:
            try:
                filtered_records = self.db_manager.filter_requests(filters)
                self.table.load_data(filtered_records)
                self.status_var.set(f"🔍 Filtered: {len(filtered_records)} records")
            except Exception as e:
                show_error_message("Filter Error", f"Failed to apply filters: {e}")
        else:
            self.load_data()

    def clear_filters(self) -> None:
        """Clear all filters and reload data."""
        self.date_filter.set("")
        self.ip_filter.set("")
        self.name_filter.set("")
        self.load_data()

    def new_request(self) -> None:
        """Create a new request (switch to form tab and clear form)."""
        self.notebook.select(0)  # Select form tab
        self.form.clear_form()
        self.status_var.set("📝 Ready for new request")

    def view_all_requests(self) -> None:
        """View all requests (switch to table tab)."""
        self.notebook.select(1)  # Select table tab
        self.load_data()

    def show_filter_dialog(self) -> None:
        """Show filter dialog (switch to table tab and focus on filters)."""
        self.notebook.select(1)  # Select table tab
        self.status_var.set("🔍 Use filters above the table to filter records")

    def export_csv(self) -> None:
        """Export all records to CSV file."""
        try:
            records = self.db_manager.get_all_requests()
            if not records:
                show_error_message("No Data", "No records to export")
                return

            filename = save_file_dialog("Export to CSV", ".csv")
            if filename:
                if FileHandler.export_to_csv(records, filename):
                    show_success_message(
                        "Export Success", f"Data exported to {filename}"
                    )
                    self.status_var.set(f"📄 Exported {len(records)} records to CSV")
                else:
                    show_error_message("Export Error", "Failed to export data to CSV")

        except Exception as e:
            show_error_message("Export Error", f"Failed to export data: {e}")

    def export_txt(self) -> None:
        """Export non-expired records to TXT file."""
        try:
            records = self.db_manager.get_non_expired_requests()
            if not records:
                show_error_message("No Data", "No non-expired records to export")
                return

            filename = save_file_dialog("Export to TXT", ".txt")
            if filename:
                if FileHandler.export_to_txt(records, filename):
                    show_success_message(
                        "Export Success", f"Non-expired records exported to {filename}"
                    )
                    self.status_var.set(
                        f"📄 Exported {len(records)} non-expired records to TXT"
                    )
                else:
                    show_error_message("Export Error", "Failed to export data to TXT")

        except Exception as e:
            show_error_message("Export Error", f"Failed to export data: {e}")

    def check_expired_records(self) -> None:
        """Manually check for expired records and show notification."""
        try:
            records = self.db_manager.get_all_requests()
            expired_records = ExpirationValidator.get_expired_records(records)

            if expired_records:
                ExpirationValidator.show_expiration_notification(expired_records)
                self.status_var.set(f"⚠️ Found {len(expired_records)} expired records")
            else:
                messagebox.showinfo("Expiration Check", "✅ No expired records found!")
                self.status_var.set("✅ No expired records found")
        except Exception as e:
            show_error_message("Check Error", f"Failed to check expired records: {e}")

    # SSL Checker Methods

    def load_ssl_data(self) -> None:
        """Load SSL data from database and refresh the SSL table."""
        try:
            records = self.db_manager.get_all_ssl_records()
            self.ssl_table.load_data(records)
            self.status_var.set(f"🔒 Loaded {len(records)} SSL records")
        except Exception as e:
            show_error_message("Database Error", f"Failed to load SSL data: {e}")
            self.status_var.set("❌ Error loading SSL data")

    def on_ssl_form_submit(self, form_data: Dict) -> None:
        """
        Handle SSL form submission.

        Args:
            form_data: Validated SSL form data
        """
        try:
            if form_data.get("id"):
                # Update existing SSL record
                success = self.db_manager.update_ssl_record(form_data["id"], form_data)
                if success:
                    self.load_ssl_data()
                    self.ssl_form.clear_form()  # Clear form after successful update
                    show_success_message("Success", "SSL record updated successfully!")
                    self.status_var.set("✅ SSL record updated successfully")
                else:
                    show_error_message("Update Error", "Failed to update SSL record")
            else:
                # Add new SSL record
                record_id = self.db_manager.add_ssl_record(form_data)
                self.load_ssl_data()
                self.ssl_form.clear_form()  # Clear form after successful addition
                show_success_message(
                    "Success",
                    f"New SSL record added successfully with ID: {record_id}!",
                )
                self.status_var.set(f"✅ New SSL record added with ID: {record_id}")

        except Exception as e:
            show_error_message("Database Error", f"Failed to save SSL record: {e}")

    def on_edit_ssl_record(self, record: Dict) -> None:
        """
        Handle edit SSL record request from table.

        Args:
            record: SSL record data to edit
        """
        # Switch to SSL form tab and load record
        self.notebook.select(2)  # Select SSL form tab
        self.ssl_form.load_record(record)
        self.status_var.set(
            f"✏️ Editing SSL record for {record.get('application_name', 'Unknown')}"
        )

    def on_delete_ssl_record(self, record_id: int) -> None:
        """
        Handle delete SSL record request from table.

        Args:
            record_id: ID of SSL record to delete
        """
        try:
            success = self.db_manager.delete_ssl_record(record_id)
            if success:
                self.load_ssl_data()
                self.status_var.set("🗑️ SSL record deleted successfully")
            else:
                show_error_message("Delete Error", "Failed to delete SSL record")
        except Exception as e:
            show_error_message("Database Error", f"Failed to delete SSL record: {e}")

    def on_update_ssl_record(self, record_id: int, ssl_data: Dict) -> None:
        """
        Handle SSL record update after scan completion.

        Args:
            record_id: ID of SSL record to update
            ssl_data: Updated SSL data from scan
        """
        try:
            success = self.db_manager.update_ssl_record(record_id, ssl_data)
            if success:
                self.load_ssl_data()  # Refresh the table to show updated data
                self.status_var.set("🔍 SSL scan data saved to database")
            else:
                show_error_message("Update Error", "Failed to save SSL scan data")
        except Exception as e:
            show_error_message("Database Error", f"Failed to save SSL scan data: {e}")

    def new_ssl_record(self) -> None:
        """Create a new SSL record (switch to SSL form tab and clear form)."""
        self.notebook.select(2)  # Select SSL form tab
        self.ssl_form.clear_form()
        self.status_var.set("🔒 Ready for new SSL record")

    def view_ssl_records(self) -> None:
        """View all SSL records (switch to SSL table tab)."""
        self.notebook.select(3)  # Select SSL table tab
        self.load_ssl_data()

    def export_ssl_csv(self) -> None:
        """Export all SSL records to CSV file."""
        try:
            records = self.db_manager.get_all_ssl_records()
            if not records:
                show_error_message("No Data", "No SSL records to export")
                return

            filename = save_file_dialog("Export SSL Records to CSV", ".csv")
            if filename:
                if FileHandler.export_ssl_to_csv(records, filename):
                    show_success_message(
                        "Export Success", f"SSL records exported to {filename}"
                    )
                    self.status_var.set(
                        f"📄 Exported {len(records)} SSL records to CSV"
                    )
                else:
                    show_error_message(
                        "Export Error", "Failed to export SSL records to CSV"
                    )

        except Exception as e:
            show_error_message("Export Error", f"Failed to export SSL records: {e}")

    def export_ssl_scan_history(self) -> None:
        """Export SSL scan history to CSV file."""
        try:
            scan_history = self.db_manager.get_ssl_scan_history()
            if not scan_history:
                show_error_message(
                    "No Scan History",
                    "No SSL scan history data to export. Please perform SSL scans first.",
                )
                return

            filename = save_file_dialog("Export SSL Scan History to CSV", ".csv")
            if filename:
                if FileHandler.export_ssl_scan_history_to_csv(
                    scan_history, filename, include_statistics=True
                ):
                    show_success_message(
                        "Export Success",
                        f"SSL scan history exported to {filename}\n\n"
                        f"Exported {len(scan_history)} scan records with statistics.",
                    )
                    self.status_var.set(
                        f"📄 Exported {len(scan_history)} SSL scan records to CSV"
                    )
                else:
                    show_error_message(
                        "Export Error", "Failed to export SSL scan history to CSV"
                    )

        except Exception as e:
            show_error_message(
                "Export Error", f"Failed to export SSL scan history: {e}"
            )

    def show_ssl_scan_statistics(self) -> None:
        """Show SSL scan statistics in a dialog."""
        try:
            stats = self.db_manager.get_ssl_scan_statistics()

            stats_text = f"""📊 SSL Scan Statistics

📋 Record Summary:
• Total SSL Records: {stats['total_records']}
• Scanned Records: {stats['scanned_records']}
• Unscanned Records: {stats['unscanned_records']}
• Scan Coverage: {stats['scan_coverage_percentage']}%

🔒 SSL Certificate Status:
• Valid Certificates: {stats['valid_ssl']}
• Expired Certificates: {stats['expired_ssl']}
• Unknown/Error Status: {stats['unknown_ssl']}

📅 Scan Activity:
• Latest Scan Date: {stats['latest_scan_date']}

💡 Recommendations:
"""

            # Add recommendations based on statistics
            recommendations = []
            if stats["unscanned_records"] > 0:
                recommendations.append(
                    f"• Scan {stats['unscanned_records']} remaining records"
                )

            if stats["expired_ssl"] > 0:
                recommendations.append(
                    f"• Review {stats['expired_ssl']} expired SSL certificates"
                )

            if stats["unknown_ssl"] > 0:
                recommendations.append(
                    f"• Re-scan {stats['unknown_ssl']} records with unknown status"
                )

            if stats["scan_coverage_percentage"] < 100:
                recommendations.append(
                    f"• Improve scan coverage from {stats['scan_coverage_percentage']}% to 100%"
                )

            if not recommendations:
                recommendations.append(
                    "• All SSL certificates are scanned and up to date!"
                )

            stats_text += "\n".join(recommendations)

            messagebox.showinfo("SSL Scan Statistics", stats_text)
            self.status_var.set("📊 SSL scan statistics displayed")

        except Exception as e:
            show_error_message(
                "Statistics Error", f"Failed to retrieve SSL scan statistics: {e}"
            )

    # Threat Analyzer Methods

    def open_threat_analyzer(self) -> None:
        """Open the threat analyzer tab."""
        self.notebook.select(4)  # Select threat analyzer tab
        self.status_var.set("🛡️ Threat Analyzer ready for log analysis")

    def show_threat_analyzer_about(self) -> None:
        """Show information about the threat analyzer."""
        about_text = """🛡️ Simple Firewall Threat Log Analyzer

A lightweight, efficient tool for analyzing firewall threat logs using only built-in Python libraries.

✨ Features:
• 📁 CSV/TXT log file upload with automatic format detection
• 🔍 Rule-based threat detection (no external dependencies)
• 📊 Statistical analysis and top threat identification
• 🚨 Security alerts and risk assessment
• 📄 Simple text and CSV report generation
• 💡 Actionable security recommendations
• ⚡ Fast processing with memory-efficient algorithms

🎯 Threat Detection Capabilities:
• High-risk port activity monitoring
• External IP denial pattern analysis
• Malware and attack keyword detection
• Geographic threat source identification
• Suspicious connection pattern recognition
• Action distribution analysis

📋 Supported Input Formats:
• Palo Alto firewall logs (CSV format)
• Generic firewall logs with standard fields
• Custom CSV files with IP, port, and action data
• Automatic delimiter detection (comma or semicolon)

🔒 Security & Privacy:
• 100% local processing - no cloud dependencies
• In-memory analysis - no data persistence
• No external libraries required for core functionality
• Lightweight and portable solution

💻 System Requirements:
• Python 3.6+ with built-in libraries only
• Minimal memory footprint
• Cross-platform compatibility"""

        messagebox.showinfo("About Simple Threat Analyzer", about_text)

    # Cybersecurity Awareness Activity Methods

    def load_awareness_data(self) -> None:
        """Load awareness activities from database and refresh the table."""
        try:
            activities = self.db_manager.get_all_awareness_activities()
            self.awareness_table.load_data(activities)
            self.status_var.set(f"📋 Loaded {len(activities)} awareness activities")
        except Exception as e:
            show_error_message("Database Error", f"Failed to load awareness activities: {e}")
            self.status_var.set("❌ Error loading awareness activities")

    def on_awareness_form_submit(self, form_data: Dict) -> None:
        """
        Handle awareness activity form submission.

        Args:
            form_data: Validated form data
        """
        try:
            if form_data.get('id'):
                # Update existing activity
                success = self.db_manager.update_awareness_activity(form_data['id'], form_data)
                if success:
                    self.load_awareness_data()
                    self.awareness_form.clear_form()
                    show_success_message("Success", "Awareness activity updated successfully!")
                    self.status_var.set("✅ Activity updated successfully")
                else:
                    show_error_message("Update Error", "Failed to update awareness activity")
            else:
                # Add new activity
                activity_id = self.db_manager.add_awareness_activity(form_data)
                self.load_awareness_data()
                self.awareness_form.clear_form()
                show_success_message("Success", f"New awareness activity added successfully with ID: {activity_id}!")
                self.status_var.set(f"✅ New activity added with ID: {activity_id}")

        except Exception as e:
            show_error_message("Database Error", f"Failed to save awareness activity: {e}")

    def on_edit_awareness_activity(self, record: Dict) -> None:
        """
        Handle edit awareness activity request from table.

        Args:
            record: Activity record data to edit
        """
        # Switch to awareness form tab and load record
        self.notebook.select(4)  # Select awareness form tab
        self.awareness_form.load_record(record)
        self.status_var.set(f"✏️ Editing awareness activity: {record.get('topic_subject', 'Unknown')}")

    def on_delete_awareness_activity(self, activity_id: int) -> None:
        """
        Handle delete awareness activity request from table.

        Args:
            activity_id: ID of activity to delete
        """
        try:
            success = self.db_manager.delete_awareness_activity(activity_id)
            if success:
                self.load_awareness_data()
                self.status_var.set("🗑️ Awareness activity deleted successfully")
            else:
                show_error_message("Delete Error", "Failed to delete awareness activity")
        except Exception as e:
            show_error_message("Database Error", f"Failed to delete awareness activity: {e}")

    def new_awareness_activity(self) -> None:
        """Create a new awareness activity (switch to form tab and clear form)."""
        self.notebook.select(4)  # Select awareness form tab
        self.awareness_form.clear_form()
        self.status_var.set("📝 Ready for new awareness activity")

    def view_awareness_activities(self) -> None:
        """View all awareness activities (switch to table tab)."""
        self.notebook.select(5)  # Select awareness table tab
        self.load_awareness_data()

    def export_awareness_csv(self) -> None:
        """Export all awareness activities to CSV file."""
        try:
            activities = self.db_manager.get_all_awareness_activities()
            if not activities:
                show_error_message("No Data", "No awareness activities to export")
                return

            filename = save_file_dialog("Export Awareness Activities to CSV", ".csv")
            if filename:
                if FileHandler.export_awareness_activities_to_csv(activities, filename):
                    show_success_message("Export Success", f"Awareness activities exported to {filename}")
                    self.status_var.set(f"📄 Exported {len(activities)} awareness activities to CSV")
                else:
                    show_error_message("Export Error", "Failed to export awareness activities to CSV")

        except Exception as e:
            show_error_message("Export Error", f"Failed to export awareness activities: {e}")

    def refresh_all_responsive_components(self):
        """Refresh all responsive components when breakpoint changes."""
        try:
            # Refresh awareness form layout if it exists
            if hasattr(self, 'awareness_form') and hasattr(self.awareness_form, 'refresh_layout'):
                self.awareness_form.refresh_layout()

            # Refresh awareness table layout if it exists
            if hasattr(self, 'awareness_table') and hasattr(self.awareness_table, 'refresh_layout'):
                self.awareness_table.refresh_layout()

            # Refresh SSL table layout (already has responsive design)
            if hasattr(self, 'ssl_table') and hasattr(self.ssl_table, 'on_breakpoint_change'):
                self.ssl_table.on_breakpoint_change(self.responsive_manager.current_breakpoint)

            # Refresh main table layout (already has responsive design)
            if hasattr(self, 'table') and hasattr(self.table, 'on_breakpoint_change'):
                self.table.on_breakpoint_change(self.responsive_manager.current_breakpoint)

            # Update status
            self.status_var.set(f"📱 Layout updated for {self.responsive_manager.current_breakpoint} screen")

        except Exception as e:
            print(f"Error refreshing responsive components: {e}")

    def test_responsive_fix(self):
        """Test method to verify the responsive fix is working."""
        print("Responsive fix is active - no update_breakpoint method needed")



    def apply_ssl_filters(self) -> None:
        """Apply filters to the SSL table data."""
        filters = {}

        # Handle None values safely in SSL filters
        ssl_app_filter = self.ssl_app_name_filter.get()
        ssl_app_filter_str = (
            str(ssl_app_filter).strip() if ssl_app_filter is not None else ""
        )
        if ssl_app_filter_str:
            filters["application_name"] = ssl_app_filter_str

        if filters:
            try:
                filtered_records = self.db_manager.filter_ssl_records(filters)
                self.ssl_table.load_data(filtered_records)
                self.status_var.set(f"🔍 Filtered: {len(filtered_records)} SSL records")
            except Exception as e:
                show_error_message("Filter Error", f"Failed to apply SSL filters: {e}")
        else:
            self.load_ssl_data()

    def clear_ssl_filters(self) -> None:
        """Clear all SSL filters and reload data."""
        self.ssl_app_name_filter.set("")
        self.load_ssl_data()

    def show_keyboard_shortcuts(self) -> None:
        """Show keyboard shortcuts dialog."""
        shortcuts_text = """⌨️ Keyboard Shortcuts

📝 General:
• Ctrl+N - New Social Media Access Request
• Ctrl+M - View All Social Media Access Requests
• Ctrl+R or F5 - Refresh Data

🔒 SSL Features:
• Ctrl+S - New SSL Record
• Ctrl+L - View All SSL Records
• Ctrl+H - Export SSL Scan History
• Ctrl+Shift+S - Show SSL Scan Statistics

📋 Cybersecurity Awareness:
• Ctrl+A - New Awareness Activity
• Ctrl+Shift+A - View All Awareness Activities

🛡️ Threat Analyzer:
• Ctrl+G - Open Threat Analyzer

💡 Tip: Use these shortcuts for faster navigation and operations!"""

        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)

    def show_about(self) -> None:
        """Show modern about dialog."""
        about_text = """🛡️ CSO Tools

Version 1.0 - CSO Tools

A secure, responsive Python desktop application for managing
social media access requests and SSL certificate monitoring.

✨ Features:
• 📱 Modern responsive design that adapts to screen size
• 🔒 Secure data entry with comprehensive validation
• 💾 SQLite database with optimized performance
• 📊 Export to CSV and TXT formats
• 🔍 Advanced filtering and sorting capabilities
• 📎 Secure file upload support (PDF or PNG only)
• 🎨 Web-like modern interface
• 📐 Automatic layout adjustment
• ⚠️ Automatic expiration validation and notifications
• 🔐 SSL Certificate monitoring and validation
• 🌐 Real-time SSL scanning capabilities
• 📋 SSL expiration tracking and alerts
• 📊 SSL scan history export and statistics
• 👁️ File viewing functionality for PDF/PNG files
• 📚 Cybersecurity awareness activity tracking
• 🛡️ Firewall threat log analysis

🛠️ Built with:
• Python 3.x
• Tkinter with modern styling
• SQLite database
• SSL/TLS certificate validation
• Responsive design principles
"""

        messagebox.showinfo("About Firewall Manager", about_text)

    def on_closing(self) -> None:
        """Handle application closing."""
        if show_confirmation_dialog("Exit", "Are you sure you want to exit?"):
            self.root.destroy()

    def run(self) -> None:
        """Start the application main loop."""
        # ResponsiveManager already handles window resize events
        # No need to bind here as it would override the ResponsiveManager's binding
        self.root.mainloop()


def main():
    """Main entry point for the application."""
    try:
        app = FirewallAccessApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Application Error", f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
