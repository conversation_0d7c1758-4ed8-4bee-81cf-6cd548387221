"""
Utility functions for Firewall Social Media Access application.
Includes validation, file handling, and export functionality.
"""

import re
import os
import shutil
import csv
import subprocess
import sys
import platform
from datetime import datetime
from typing import Dict, List, Optional
from tkinter import messagebox, filedialog


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class InputValidator:
    """Handles input validation for form fields."""

    @staticmethod
    def validate_ip_address(ip: str) -> bool:
        """
        Validate IP address format.

        Args:
            ip: IP address string to validate

        Returns:
            True if valid, False otherwise
        """
        # Handle None values safely
        if ip is None:
            return False

        ip_str = str(ip).strip()
        if not ip_str:
            return False

        # Basic IPv4 validation pattern
        pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(pattern, ip_str))

    @staticmethod
    def validate_required_field(value: str, field_name: str) -> str:
        """
        Validate that a required field is not empty.

        Args:
            value: Field value to validate
            field_name: Name of the field for error messages

        Returns:
            Cleaned field value

        Raises:
            ValidationError: If field is empty or invalid
        """
        # Handle None values safely
        if value is None:
            raise ValidationError(f"{field_name} is required")

        value_str = str(value).strip()
        if not value_str:
            raise ValidationError(f"{field_name} is required")

        # Check for potentially dangerous characters
        if any(char in value_str for char in ['<', '>', '"', "'"]):
            raise ValidationError(f"{field_name} contains invalid characters")

        return value_str

    @staticmethod
    def validate_date(date_str: str) -> bool:
        """
        Validate date format (YYYY-MM-DD) with strict format checking.

        Args:
            date_str: Date string to validate

        Returns:
            True if valid, False otherwise
        """
        # Handle None values safely
        if date_str is None:
            return False

        date_str_clean = str(date_str).strip()
        if not date_str_clean:
            return False

        # Check exact format first
        if len(date_str_clean) != 10 or date_str_clean.count('-') != 2:
            return False

        # Check pattern YYYY-MM-DD
        parts = date_str_clean.split('-')
        if len(parts) != 3:
            return False

        year, month, day = parts
        if len(year) != 4 or len(month) != 2 or len(day) != 2:
            return False

        if not (year.isdigit() and month.isdigit() and day.isdigit()):
            return False

        try:
            parsed_date = datetime.strptime(date_str, '%Y-%m-%d')
            # Additional validation: ensure it's a reasonable date
            if parsed_date.year < 1900 or parsed_date.year > 2100:
                return False
            return True
        except ValueError:
            return False

    @staticmethod
    def validate_form_data(form_data: Dict) -> Dict:
        """
        Validate all form data and return cleaned data.

        Args:
            form_data: Dictionary containing form field values

        Returns:
            Dictionary containing validated and cleaned data

        Raises:
            ValidationError: If any validation fails
        """
        validated_data = {}

        # Validate IP address
        if not InputValidator.validate_ip_address(form_data.get('ip_address', '')):
            raise ValidationError("Invalid IP address format")
        validated_data['ip_address'] = form_data['ip_address'].strip()

        # Validate required text fields
        required_fields = ['employee_name', 'section', 'position']
        for field in required_fields:
            validated_data[field] = InputValidator.validate_required_field(
                form_data.get(field, ''), field.replace('_', ' ').title()
            )

        # Validate date requested
        if not InputValidator.validate_date(form_data.get('date_requested', '')):
            raise ValidationError("Invalid date requested format. Please use exact format: YYYY-MM-DD (e.g., 2024-01-15)")
        validated_data['date_requested'] = form_data['date_requested']

        # Validate request type
        if form_data.get('request_type') not in ['Temporary', 'Permanent']:
            raise ValidationError("Invalid request type")
        validated_data['request_type'] = form_data['request_type']

        # Validate is_expired
        if form_data.get('is_expired') not in ['Yes', 'No']:
            raise ValidationError("Invalid expired status")
        validated_data['is_expired'] = form_data['is_expired']

        # Validate date_expiration for temporary requests
        if validated_data['request_type'] == 'Temporary':
            if not InputValidator.validate_date(form_data.get('date_expiration', '')):
                raise ValidationError("Expiration date is required for temporary requests. Please use exact format: YYYY-MM-DD (e.g., 2024-01-15)")
            validated_data['date_expiration'] = form_data['date_expiration']
        else:
            validated_data['date_expiration'] = None

        # Validate is_ipr_reserved
        if form_data.get('is_ipr_reserved') not in ['Yes', 'No']:
            raise ValidationError("Invalid IPR reserved status")
        validated_data['is_ipr_reserved'] = form_data['is_ipr_reserved']

        # Optional fields - handle None values safely
        remarks = form_data.get('remarks', '')
        validated_data['remarks'] = str(remarks).strip() if remarks is not None else ''

        verification_file = form_data.get('verification_file_path', '')
        validated_data['verification_file_path'] = str(verification_file).strip() if verification_file is not None else ''

        return validated_data


class FileHandler:
    """Handles file operations including upload and export."""

    ALLOWED_EXTENSIONS = {'.pdf','.png'}  # Only PDF and PNG files allowed
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

    @staticmethod
    def validate_file(file_path: str) -> bool:
        """
        Validate uploaded file.

        Args:
            file_path: Path to the file to validate

        Returns:
            True if file is valid, False otherwise
        """
        if not os.path.exists(file_path):
            return False

        # Check file extension
        _, ext = os.path.splitext(file_path.lower())
        if ext not in FileHandler.ALLOWED_EXTENSIONS:
            return False

        # Check file size
        if os.path.getsize(file_path) > FileHandler.MAX_FILE_SIZE:
            return False

        return True

    @staticmethod
    def copy_verification_file(source_path: str, employee_name: str) -> str:
        """
        Copy verification file to secure location.

        Args:
            source_path: Original file path
            employee_name: Employee name for file naming

        Returns:
            New file path

        Raises:
            Exception: If file operation fails
        """
        if not FileHandler.validate_file(source_path):
            raise Exception("Invalid file format or size")

        # Create uploads directory if it doesn't exist
        uploads_dir = "uploads"
        os.makedirs(uploads_dir, exist_ok=True)

        # Generate secure filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        _, ext = os.path.splitext(source_path)
        safe_employee_name = re.sub(r'[^\w\-_\.]', '_', employee_name)
        new_filename = f"{safe_employee_name}_{timestamp}{ext}"
        new_path = os.path.join(uploads_dir, new_filename)

        # Copy file
        shutil.copy2(source_path, new_path)
        return new_path

    @staticmethod
    def export_to_csv(data: List[Dict], filename: str) -> bool:
        """
        Export data to CSV file.

        Args:
            data: List of dictionaries containing data to export
            filename: Output filename

        Returns:
            True if export successful, False otherwise
        """
        try:
            if not data:
                return False

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for row in data:
                    writer.writerow(row)

            return True

        except Exception as e:
            print(f"CSV export error: {e}")
            return False

    @staticmethod
    def export_to_txt(data: List[Dict], filename: str) -> bool:
        """
        Export non-expired data to TXT file in specified format.
        Format: [IP Address] ; [Employee Name] - [Section] - [Position]

        Args:
            data: List of dictionaries containing data to export
            filename: Output filename

        Returns:
            True if export successful, False otherwise
        """
        try:
            with open(filename, 'w', encoding='utf-8') as txtfile:
                for row in data:
                    if row.get('is_expired') == 'No':
                        line = f"{row['ip_address']} ; {row['employee_name']} - {row['section']} - {row['position']}\n"
                        txtfile.write(line)

            return True

        except Exception as e:
            print(f"TXT export error: {e}")
            return False

    @staticmethod
    def export_ssl_to_csv(data: List[Dict], filename: str) -> bool:
        """
        Export SSL records to CSV file.

        Args:
            data: List of SSL record dictionaries
            filename: Output filename

        Returns:
            True if export successful, False otherwise
        """
        try:
            if not data:
                return False

            # Define SSL-specific headers
            ssl_headers = [
                'id', 'application_name', 'ip_address', 'ip_url', 'remarks',
                'ssl_valid', 'ssl_expiration_date', 'expiration_status',
                'last_scan_date', 'created_at', 'updated_at'
            ]

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=ssl_headers)
                writer.writeheader()

                for row in data:
                    # Create a filtered row with only SSL-relevant fields
                    ssl_row = {header: row.get(header, '') for header in ssl_headers}
                    writer.writerow(ssl_row)

            return True

        except Exception as e:
            print(f"SSL CSV export error: {e}")
            return False

    @staticmethod
    def export_ssl_scan_history_to_csv(data: List[Dict], filename: str, include_statistics: bool = True) -> bool:
        """
        Export SSL scan history to CSV file with optional statistics.

        Args:
            data: List of SSL scan history dictionaries
            filename: Output filename
            include_statistics: Whether to include scan statistics at the top

        Returns:
            True if export successful, False otherwise
        """
        try:
            if not data:
                return False

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header information
                writer.writerow(['SSL Scan History Export'])
                writer.writerow(['Generated on:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                writer.writerow([])  # Empty row

                # Write statistics if requested
                if include_statistics:
                    writer.writerow(['=== SCAN STATISTICS ==='])

                    # Calculate statistics from the data
                    total_scans = len(data)
                    valid_count = sum(1 for record in data if record.get('ssl_valid') == 'Valid')
                    expired_count = sum(1 for record in data if record.get('ssl_valid') == 'Expired')
                    unknown_count = sum(1 for record in data if record.get('ssl_valid') not in ['Valid', 'Expired'])

                    # Get date range
                    scan_dates = [record.get('last_scan_date', '') for record in data if record.get('last_scan_date')]
                    scan_dates = [date for date in scan_dates if date]

                    if scan_dates:
                        earliest_scan = min(scan_dates)
                        latest_scan = max(scan_dates)
                    else:
                        earliest_scan = latest_scan = 'N/A'

                    writer.writerow(['Total Scanned Records:', total_scans])
                    writer.writerow(['Valid SSL Certificates:', valid_count])
                    writer.writerow(['Expired SSL Certificates:', expired_count])
                    writer.writerow(['Unknown/Error Status:', unknown_count])
                    writer.writerow(['Earliest Scan Date:', earliest_scan])
                    writer.writerow(['Latest Scan Date:', latest_scan])
                    writer.writerow([])  # Empty row

                # Write scan history data
                writer.writerow(['=== SSL SCAN HISTORY ==='])

                # Define scan history headers
                scan_headers = [
                    'Record ID',
                    'Application Name',
                    'IP Address',
                    'IP URL',
                    'SSL Status',
                    'SSL Expiration Date',
                    'Expiration Status',
                    'Last Scan Date',
                    'Record Created',
                    'Last Updated'
                ]

                writer.writerow(scan_headers)

                # Write scan data
                for record in data:
                    row = [
                        record.get('id', ''),
                        record.get('application_name', ''),
                        record.get('ip_address', ''),
                        record.get('ip_url', ''),
                        record.get('ssl_valid', 'Unknown'),
                        record.get('ssl_expiration_date', ''),
                        record.get('expiration_status', 'No'),
                        record.get('last_scan_date', ''),
                        record.get('created_at', ''),
                        record.get('updated_at', '')
                    ]
                    writer.writerow(row)

            return True

        except Exception as e:
            print(f"SSL scan history CSV export error: {e}")
            return False

    @staticmethod
    def export_awareness_activities_to_csv(activities: List[Dict], filename: str) -> bool:
        """
        Export cybersecurity awareness activities to CSV file.

        Args:
            activities: List of awareness activity dictionaries
            filename: Output filename

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'ID', 'Topic/Subject', 'Date', 'Number of Participants',
                    'Pre-test Result (%)', 'Post-test Result (%)', 'Remarks',
                    'Created At', 'Updated At'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for activity in activities:
                    # Format test results
                    pretest = activity.get('pretest_result')
                    posttest = activity.get('posttest_result')

                    writer.writerow({
                        'ID': activity.get('id', ''),
                        'Topic/Subject': activity.get('topic_subject', ''),
                        'Date': activity.get('activity_date', ''),
                        'Number of Participants': activity.get('number_of_pax', ''),
                        'Pre-test Result (%)': f"{pretest:.1f}" if pretest is not None else '',
                        'Post-test Result (%)': f"{posttest:.1f}" if posttest is not None else '',
                        'Remarks': activity.get('remarks', ''),
                        'Created At': activity.get('created_at', ''),
                        'Updated At': activity.get('updated_at', '')
                    })

            return True

        except Exception as e:
            print(f"Error exporting awareness activities to CSV: {e}")
            return False

    @staticmethod
    def view_file(file_path: str) -> bool:
        """
        Open and view a file using the system's default application.
        Supports PDF and PNG files.

        Args:
            file_path: Path to the file to view

        Returns:
            True if file was opened successfully, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                show_error_message("File Not Found", f"The file '{file_path}' does not exist.")
                return False

            # Check file extension
            _, ext = os.path.splitext(file_path.lower())
            if ext not in ['.pdf', '.png', '.jpg', '.jpeg']:
                show_error_message("Unsupported File Type",
                                 f"Cannot view files of type '{ext}'. Supported types: PDF, PNG, JPG, JPEG")
                return False

            # Open file with system default application
            system = platform.system()

            if system == "Windows":
                # Windows
                os.startfile(file_path)
            elif system == "Darwin":
                # macOS
                subprocess.run(["open", file_path], check=True)
            else:
                # Linux and other Unix-like systems
                subprocess.run(["xdg-open", file_path], check=True)

            return True

        except subprocess.CalledProcessError:
            show_error_message("View Error",
                             "Failed to open the file. Please ensure you have a suitable application installed.")
            return False
        except Exception as e:
            show_error_message("View Error", f"An error occurred while trying to view the file: {e}")
            return False

    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, str]:
        """
        Get information about a file.

        Args:
            file_path: Path to the file

        Returns:
            Dictionary containing file information
        """
        try:
            if not os.path.exists(file_path):
                return {"error": "File not found"}

            stat = os.stat(file_path)
            _, ext = os.path.splitext(file_path)
            filename = os.path.basename(file_path)

            return {
                "filename": filename,
                "extension": ext.lower(),
                "size": f"{stat.st_size / 1024:.1f} KB",
                "modified": datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                "full_path": file_path
            }

        except Exception as e:
            return {"error": f"Failed to get file info: {e}"}


def show_error_message(title: str, message: str) -> None:
    """
    Show error message dialog.

    Args:
        title: Dialog title
        message: Error message
    """
    messagebox.showerror(title, message)


def show_success_message(title: str, message: str) -> None:
    """
    Show success message dialog.

    Args:
        title: Dialog title
        message: Success message
    """
    messagebox.showinfo(title, message)


def show_confirmation_dialog(title: str, message: str) -> bool:
    """
    Show confirmation dialog.

    Args:
        title: Dialog title
        message: Confirmation message

    Returns:
        True if user confirms, False otherwise
    """
    return messagebox.askyesno(title, message)


def select_file_dialog(title: str = "Select PDF File") -> Optional[str]:
    """
    Show file selection dialog for PDF files only.

    Args:
        title: Dialog title

    Returns:
        Selected file path or None if cancelled
    """
    filetypes = [
        ("PDF files", "*.pdf"),
        ("PNG files", "*.png"),
        ("All files", "*.*")
    ] #Added PNG support

    return filedialog.askopenfilename(
        title=title,
        filetypes=filetypes
    )


def save_file_dialog(title: str = "Save File", default_extension: str = ".csv") -> Optional[str]:
    """
    Show save file dialog.

    Args:
        title: Dialog title
        default_extension: Default file extension

    Returns:
        Selected file path or None if cancelled
    """
    filetypes = []
    if default_extension == ".csv":
        filetypes = [("CSV files", "*.csv"), ("All files", "*.*")]
    elif default_extension == ".txt":
        filetypes = [("Text files", "*.txt"), ("All files", "*.*")]
    else:
        filetypes = [("All files", "*.*")]

    return filedialog.asksaveasfilename(
        title=title,
        defaultextension=default_extension,
        filetypes=filetypes
    )
