"""
Cybersecurity Awareness Activity Table View Module

Provides table interface for viewing and managing cybersecurity awareness activities.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Callable, Optional
import re

from modern_ui import (
    ModernCard, ModernButton, ModernTheme, get_responsive_padding
)
from utils import show_error_message, show_success_message


class AwarenessActivityTable:
    """
    Table view for cybersecurity awareness activities.
    """
    
    def __init__(self, parent_frame, on_edit_callback: Callable, on_delete_callback: Callable, 
                responsive_manager):
        """
        Initialize the awareness activity table.
        
        Args:
            parent_frame: Parent tkinter frame
            on_edit_callback: Callback function for editing a record
            on_delete_callback: Callback function for deleting a record
            responsive_manager: Responsive layout manager
        """
        self.parent_frame = parent_frame
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        self.responsive_manager = responsive_manager
        
        # Table data
        self.data = []
        self.selected_id = None
        
        # Create table
        self.create_table()
    
    def create_table(self):
        """Create the awareness activity table."""
        # Table card
        self.table_card = ModernCard(self.parent_frame, title="📋 Cybersecurity Awareness Activities")
        self.table_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                           padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
                           pady=10)
        
        # Table content
        table_content = ttk.Frame(self.table_card, style='Surface.TFrame')
        self.table_card.add_content(table_content)
        table_content.columnconfigure(0, weight=1)
        table_content.rowconfigure(1, weight=1)
        
        # Action buttons
        action_frame = ttk.Frame(table_content, style='Surface.TFrame')
        action_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_SM))
        
        if self.responsive_manager.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.edit_button = ModernButton(
                action_frame, variant="primary", text="✏️ Edit Selected", command=self.edit_selected
            )
            self.edit_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))
            
            self.delete_button = ModernButton(
                action_frame, variant="danger", text="🗑️ Delete Selected", command=self.delete_selected
            )
            self.delete_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))
            
            self.export_button = ModernButton(
                action_frame, variant="warning", text="📊 Export to CSV", command=self.export_csv
            )
            self.export_button.pack(fill=tk.X)
        else:
            # Desktop: side by side buttons
            self.edit_button = ModernButton(
                action_frame, variant="primary", text="✏️ Edit Selected", command=self.edit_selected
            )
            self.edit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))
            
            self.delete_button = ModernButton(
                action_frame, variant="danger", text="🗑️ Delete Selected", command=self.delete_selected
            )
            self.delete_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))
            
            self.export_button = ModernButton(
                action_frame, variant="warning", text="📊 Export to CSV", command=self.export_csv
            )
            self.export_button.pack(side=tk.LEFT)
        
        # Initially disable action buttons
        self.edit_button.configure(state='disabled')
        self.delete_button.configure(state='disabled')
        
        # Create table with scrollbar
        table_frame = ttk.Frame(table_content, style='Surface.TFrame')
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(table_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Treeview for table
        self.tree = ttk.Treeview(
            table_frame,
            columns=('id', 'topic_subject', 'activity_date', 'number_of_pax', 
                    'pretest_result', 'posttest_result', 'remarks'),
            show='headings',
            selectmode='browse',
            yscrollcommand=scrollbar.set
        )
        
        # Configure scrollbar
        scrollbar.config(command=self.tree.yview)
        
        # Configure columns based on screen size
        self.configure_table_columns()
        
        # Pack treeview
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Bind events
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        self.tree.bind('<Double-1>', self.on_double_click)
    
    def load_data(self, data: List[Dict]):
        """
        Load data into the table.

        Args:
            data: List of dictionaries containing activity data
        """
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Store data
        self.data = data
        
        # Insert data into table based on current column configuration
        current_columns = self.tree['columns']

        for record in data:
            # Prepare values based on current columns
            values = []

            for col in current_columns:
                if col == 'id':
                    values.append(record.get('id', ''))
                elif col == 'topic_subject':
                    topic = record.get('topic_subject', '')
                    # Truncate topic for mobile view
                    if self.responsive_manager.current_breakpoint in ['xs', 'sm'] and len(topic) > 20:
                        topic = topic[:17] + "..."
                    values.append(topic)
                elif col == 'activity_date':
                    values.append(record.get('activity_date', ''))
                elif col == 'number_of_pax':
                    values.append(record.get('number_of_pax', ''))
                elif col == 'pretest_result':
                    pretest = record.get('pretest_result')
                    if pretest is not None:
                        # Show shorter format for tablet/mobile
                        if self.responsive_manager.current_breakpoint in ['xs', 'sm', 'md']:
                            values.append(f"{pretest:.0f}%")
                        else:
                            values.append(f"{pretest:.1f}%")
                    else:
                        values.append("-")
                elif col == 'posttest_result':
                    posttest = record.get('posttest_result')
                    if posttest is not None:
                        # Show shorter format for tablet/mobile
                        if self.responsive_manager.current_breakpoint in ['xs', 'sm', 'md']:
                            values.append(f"{posttest:.0f}%")
                        else:
                            values.append(f"{posttest:.1f}%")
                    else:
                        values.append("-")
                elif col == 'remarks':
                    remarks = record.get('remarks', '')
                    if remarks and len(remarks) > 30:
                        remarks = remarks[:27] + "..."
                    values.append(remarks)

            self.tree.insert('', 'end', values=tuple(values))
        
        # Reset selection
        self.selected_id = None
        self.edit_button.configure(state='disabled')
        self.delete_button.configure(state='disabled')
    
    def on_select(self, event=None):
        """Handle selection event."""
        selected_items = self.tree.selection()
        if selected_items:
            # Get selected item values
            item_values = self.tree.item(selected_items[0], 'values')
            if item_values:
                self.selected_id = item_values[0]
                
                # Enable action buttons
                self.edit_button.configure(state='normal')
                self.delete_button.configure(state='normal')
        else:
            self.selected_id = None
            
            # Disable action buttons
            self.edit_button.configure(state='disabled')
            self.delete_button.configure(state='disabled')
    
    def on_double_click(self, event=None):
        """Handle double-click event on table row."""
        self.edit_selected()
    
    def edit_selected(self):
        """Edit the selected record."""
        if self.selected_id is None:
            show_error_message("No Selection", "Please select an activity to edit")
            return
        
        # Find the selected record
        selected_record = None
        for record in self.data:
            if str(record.get('id', '')) == str(self.selected_id):
                selected_record = record
                break
        
        if selected_record:
            self.on_edit_callback(selected_record)
        else:
            show_error_message("Record Not Found", "The selected record could not be found")
    
    def delete_selected(self):
        """Delete the selected record."""
        if self.selected_id is None:
            show_error_message("No Selection", "Please select an activity to delete")
            return
        
        # Confirm deletion
        confirm = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete the selected activity (ID: {self.selected_id})?\n\nThis action cannot be undone."
        )
        
        if confirm:
            self.on_delete_callback(self.selected_id)
    
    def export_csv(self):
        """Export table data to CSV."""
        if not self.data:
            show_error_message("No Data", "No awareness activities to export")
            return

        # Import here to avoid circular imports
        from utils import save_file_dialog, FileHandler

        filename = save_file_dialog("Export Awareness Activities to CSV", ".csv")
        if filename:
            if FileHandler.export_awareness_activities_to_csv(self.data, filename):
                show_success_message("Export Success", f"Awareness activities exported to {filename}")
            else:
                show_error_message("Export Error", "Failed to export awareness activities to CSV")
    
    def refresh_table(self):
        """Refresh the table data."""
        # This will be overridden in the main application
        pass
    
    def get_selected_id(self) -> Optional[int]:
        """
        Get the currently selected record ID.

        Returns:
            Selected record ID or None if no selection
        """
        return self.selected_id

    def configure_table_columns(self):
        """Configure table columns based on screen size."""
        # Check if we're on mobile
        is_mobile = self.responsive_manager.current_breakpoint in ['xs', 'sm']
        is_tablet = self.responsive_manager.current_breakpoint == 'md'

        if is_mobile:
            # Mobile: Show only essential columns
            self.tree.configure(columns=('id', 'topic_subject', 'activity_date', 'number_of_pax'))

            # Configure columns
            self.tree.column('id', width=40, anchor='center')
            self.tree.column('topic_subject', width=150, anchor='w')
            self.tree.column('activity_date', width=80, anchor='center')
            self.tree.column('number_of_pax', width=60, anchor='center')

            # Configure headings
            self.tree.heading('id', text='ID')
            self.tree.heading('topic_subject', text='Title')
            self.tree.heading('activity_date', text='Date')
            self.tree.heading('number_of_pax', text='Pax')

        elif is_tablet:
            # Tablet: Show medium set of columns
            self.tree.configure(columns=('id', 'topic_subject', 'activity_date', 'number_of_pax',
                                       'pretest_result', 'posttest_result'))

            # Configure columns
            self.tree.column('id', width=40, anchor='center')
            self.tree.column('topic_subject', width=180, anchor='w')
            self.tree.column('activity_date', width=90, anchor='center')
            self.tree.column('number_of_pax', width=70, anchor='center')
            self.tree.column('pretest_result', width=80, anchor='center')
            self.tree.column('posttest_result', width=80, anchor='center')

            # Configure headings
            self.tree.heading('id', text='ID')
            self.tree.heading('topic_subject', text='Title/Subject')
            self.tree.heading('activity_date', text='Date')
            self.tree.heading('number_of_pax', text='Participants')
            self.tree.heading('pretest_result', text='Pre-test %')
            self.tree.heading('posttest_result', text='Post-test %')

        else:
            # Desktop: Show all columns
            self.tree.configure(columns=('id', 'topic_subject', 'activity_date', 'number_of_pax',
                                       'pretest_result', 'posttest_result', 'remarks'))

            # Configure columns
            self.tree.column('id', width=50, anchor='center')
            self.tree.column('topic_subject', width=200, anchor='w')
            self.tree.column('activity_date', width=100, anchor='center')
            self.tree.column('number_of_pax', width=80, anchor='center')
            self.tree.column('pretest_result', width=100, anchor='center')
            self.tree.column('posttest_result', width=100, anchor='center')
            self.tree.column('remarks', width=200, anchor='w')

            # Configure headings
            self.tree.heading('id', text='ID')
            self.tree.heading('topic_subject', text='Title/Subject')
            self.tree.heading('activity_date', text='Date')
            self.tree.heading('number_of_pax', text='Participants')
            self.tree.heading('pretest_result', text='Pre-test Percentage')
            self.tree.heading('posttest_result', text='Post-test Percentage')
            self.tree.heading('remarks', text='Remarks')

    def refresh_layout(self):
        """Refresh the layout when window is resized."""
        # Reconfigure columns based on new screen size
        self.configure_table_columns()

        # Reload data to match new column configuration
        if self.data:
            self.load_data(self.data)
