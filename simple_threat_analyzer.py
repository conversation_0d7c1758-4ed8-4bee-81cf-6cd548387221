"""
Simple Firewall Threat Log Analyzer

A lightweight alternative approach using only built-in Python libraries.
Focuses on practical threat detection and simple reporting.
"""

import csv
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from collections import Counter, defaultdict
import ipaddress


class SimpleThreatAnalyzer:
    """
    Lightweight threat analyzer for Palo Alto firewall logs.
    Uses only built-in Python libraries for maximum compatibility.
    """
    
    def __init__(self):
        """Initialize the simple threat analyzer."""
        self.log_data: List[Dict] = []
        self.analysis_results: Dict[str, Any] = {}
        self.threat_indicators = {
            'high_risk_ports': [22, 23, 25, 135, 139, 445, 1433, 3389, 5900],
            'suspicious_countries': ['CN', 'RU', 'KP', 'IR', 'SY'],
            'malware_keywords': ['malware', 'virus', 'trojan', 'backdoor', 'botnet'],
            'attack_keywords': ['exploit', 'vulnerability', 'injection', 'overflow']
        }
    
    def load_csv_log(self, file_path: str) -> bool:
        """
        Load and parse CSV log file with basic error handling.
        
        Args:
            file_path: Path to CSV log file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.log_data = []
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                # Try to detect delimiter
                sample = file.read(1024)
                file.seek(0)
                
                delimiter = ',' if sample.count(',') > sample.count(';') else ';'
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                for row_num, row in enumerate(reader, 1):
                    if row_num > 50000:  # Limit for performance
                        break
                    
                    # Clean and normalize row data
                    cleaned_row = {}
                    for key, value in row.items():
                        if key and value:
                            cleaned_key = str(key).strip()
                            cleaned_value = str(value).strip()
                            cleaned_row[cleaned_key] = cleaned_value
                    
                    if cleaned_row:  # Only add non-empty rows
                        self.log_data.append(cleaned_row)
            
            return len(self.log_data) > 0
            
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return False
    
    def analyze_threats(self) -> Dict[str, Any]:
        """
        Perform comprehensive but lightweight threat analysis.
        
        Returns:
            Dictionary containing analysis results
        """
        if not self.log_data:
            return {}
        
        self.analysis_results = {
            'summary': self._generate_summary(),
            'threat_detections': self._detect_simple_threats(),
            'top_statistics': self._calculate_top_statistics(),
            'security_alerts': self._generate_security_alerts(),
            'recommendations': []
        }
        
        # Generate recommendations
        self.analysis_results['recommendations'] = self._generate_recommendations()
        
        return self.analysis_results
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate basic summary statistics."""
        total_logs = len(self.log_data)
        
        # Count actions
        actions = [row.get('Action', '').lower() for row in self.log_data]
        action_counts = Counter(actions)
        
        # Count unique IPs
        source_ips = set(row.get('Source address', '') for row in self.log_data)
        dest_ips = set(row.get('Destination address', '') for row in self.log_data)
        
        # Time analysis
        timestamps = []
        for row in self.log_data:
            time_str = row.get('Receive Time', '') or row.get('Generate Time', '')
            if time_str:
                try:
                    # Try common timestamp formats
                    for fmt in ['%Y/%m/%d %H:%M:%S', '%Y-%m-%d %H:%M:%S', '%m/%d/%Y %H:%M:%S']:
                        try:
                            timestamps.append(datetime.strptime(time_str, fmt))
                            break
                        except ValueError:
                            continue
                except:
                    pass
        
        time_range = {}
        if timestamps:
            time_range = {
                'start': min(timestamps),
                'end': max(timestamps),
                'duration_hours': (max(timestamps) - min(timestamps)).total_seconds() / 3600
            }
        
        return {
            'total_entries': total_logs,
            'unique_source_ips': len(source_ips),
            'unique_destination_ips': len(dest_ips),
            'action_counts': dict(action_counts),
            'time_range': time_range
        }
    
    def _detect_simple_threats(self) -> List[Dict[str, Any]]:
        """Detect threats using simple rule-based analysis."""
        threats = []
        
        # 1. High-risk port activity
        high_port_activity = []
        for row in self.log_data:
            try:
                dest_port = int(row.get('Destination Port', 0))
                if dest_port in self.threat_indicators['high_risk_ports']:
                    high_port_activity.append({
                        'source_ip': row.get('Source address', 'Unknown'),
                        'dest_port': dest_port,
                        'action': row.get('Action', 'Unknown')
                    })
            except (ValueError, TypeError):
                pass
        
        if high_port_activity:
            threats.append({
                'type': 'High-Risk Port Activity',
                'severity': 'MEDIUM',
                'count': len(high_port_activity),
                'description': f'Activity detected on {len(set(item["dest_port"] for item in high_port_activity))} high-risk ports',
                'samples': high_port_activity[:5]
            })
        
        # 2. Denied connections from external IPs
        external_denies = []
        for row in self.log_data:
            if row.get('Action', '').lower() == 'deny':
                source_ip = row.get('Source address', '')
                if self._is_external_ip(source_ip):
                    external_denies.append({
                        'source_ip': source_ip,
                        'dest_ip': row.get('Destination address', 'Unknown'),
                        'threat_type': row.get('Threat/Content Type', 'Unknown')
                    })
        
        if external_denies:
            threats.append({
                'type': 'External Denied Connections',
                'severity': 'HIGH' if len(external_denies) > 100 else 'MEDIUM',
                'count': len(external_denies),
                'description': f'Denied connections from external IP addresses',
                'samples': external_denies[:5]
            })
        
        # 3. Malware-related threats
        malware_threats = []
        for row in self.log_data:
            threat_name = row.get('Threat/Content Name', '').lower()
            category = row.get('Category', '').lower()
            
            if any(keyword in threat_name or keyword in category 
                   for keyword in self.threat_indicators['malware_keywords']):
                malware_threats.append({
                    'source_ip': row.get('Source address', 'Unknown'),
                    'threat_name': row.get('Threat/Content Name', 'Unknown'),
                    'category': row.get('Category', 'Unknown')
                })
        
        if malware_threats:
            threats.append({
                'type': 'Malware Detection',
                'severity': 'HIGH',
                'count': len(malware_threats),
                'description': 'Malware-related threats identified',
                'samples': malware_threats[:5]
            })
        
        # 4. Geographic threats
        geo_threats = []
        for row in self.log_data:
            source_country = row.get('Source Country', '')
            if source_country in self.threat_indicators['suspicious_countries']:
                geo_threats.append({
                    'source_ip': row.get('Source address', 'Unknown'),
                    'country': source_country,
                    'action': row.get('Action', 'Unknown')
                })
        
        if geo_threats:
            threats.append({
                'type': 'Geographic Threats',
                'severity': 'MEDIUM',
                'count': len(geo_threats),
                'description': f'Traffic from high-risk countries: {set(item["country"] for item in geo_threats)}',
                'samples': geo_threats[:5]
            })
        
        return threats
    
    def _calculate_top_statistics(self) -> Dict[str, Any]:
        """Calculate top statistics for reporting."""
        stats = {}
        
        # Top source IPs
        source_ips = [row.get('Source address', '') for row in self.log_data if row.get('Source address')]
        stats['top_source_ips'] = dict(Counter(source_ips).most_common(10))
        
        # Top destination ports
        dest_ports = []
        for row in self.log_data:
            try:
                port = int(row.get('Destination Port', 0))
                if port > 0:
                    dest_ports.append(port)
            except (ValueError, TypeError):
                pass
        stats['top_destination_ports'] = dict(Counter(dest_ports).most_common(10))
        
        # Top threat types
        threat_types = [row.get('Threat/Content Type', '') for row in self.log_data if row.get('Threat/Content Type')]
        stats['top_threat_types'] = dict(Counter(threat_types).most_common(10))
        
        # Top denied IPs
        denied_ips = [row.get('Source address', '') for row in self.log_data 
                     if row.get('Action', '').lower() == 'deny' and row.get('Source address')]
        stats['top_denied_ips'] = dict(Counter(denied_ips).most_common(10))
        
        return stats
    
    def _generate_security_alerts(self) -> List[Dict[str, str]]:
        """Generate security alerts based on analysis."""
        alerts = []
        
        summary = self.analysis_results.get('summary', {})
        threats = self.analysis_results.get('threat_detections', [])
        
        # Check for high threat volume
        total_threats = sum(threat['count'] for threat in threats)
        if total_threats > summary.get('total_entries', 0) * 0.1:  # More than 10%
            alerts.append({
                'level': 'HIGH',
                'message': f'High threat volume detected: {total_threats} threats in {summary.get("total_entries", 0)} log entries'
            })
        
        # Check for critical threats
        high_severity_threats = [t for t in threats if t['severity'] == 'HIGH']
        if high_severity_threats:
            alerts.append({
                'level': 'CRITICAL',
                'message': f'{len(high_severity_threats)} high-severity threat types detected requiring immediate attention'
            })
        
        # Check for unusual activity patterns
        action_counts = summary.get('action_counts', {})
        deny_count = action_counts.get('deny', 0)
        total_actions = sum(action_counts.values())
        
        if deny_count > total_actions * 0.3:  # More than 30% denied
            alerts.append({
                'level': 'MEDIUM',
                'message': f'High denial rate: {deny_count}/{total_actions} ({deny_count/total_actions*100:.1f}%) connections denied'
            })
        
        return alerts
    
    def _generate_recommendations(self) -> List[str]:
        """Generate security recommendations."""
        recommendations = []
        
        threats = self.analysis_results.get('threat_detections', [])
        alerts = self.analysis_results.get('security_alerts', [])
        
        # Recommendations based on threats
        for threat in threats:
            if threat['type'] == 'Malware Detection':
                recommendations.append(
                    "URGENT: Malware detected. Immediately isolate affected systems and run full antivirus scans."
                )
            elif threat['type'] == 'High-Risk Port Activity':
                recommendations.append(
                    "Review firewall rules for high-risk ports. Consider blocking unnecessary services."
                )
            elif threat['type'] == 'Geographic Threats':
                recommendations.append(
                    "Consider implementing geo-blocking for high-risk countries if business requirements allow."
                )
        
        # Recommendations based on alerts
        critical_alerts = [a for a in alerts if a['level'] == 'CRITICAL']
        if critical_alerts:
            recommendations.append(
                "CRITICAL: Immediate security team review required. Multiple high-severity threats detected."
            )
        
        # General recommendations
        if not recommendations:
            recommendations.append("Continue regular monitoring. No immediate threats detected.")
        
        recommendations.extend([
            "Regularly update threat intelligence feeds",
            "Review and update firewall rules quarterly",
            "Implement automated log monitoring and alerting",
            "Conduct regular security assessments"
        ])
        
        return recommendations
    
    def _is_external_ip(self, ip_str: str) -> bool:
        """Check if IP is external (not private)."""
        try:
            ip = ipaddress.ip_address(ip_str.strip())
            return not ip.is_private
        except (ValueError, AttributeError):
            return False
    
    def export_simple_report(self, output_path: str) -> bool:
        """
        Export analysis results to a simple text report.
        
        Args:
            output_path: Path for output file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("🛡️ FIREWALL THREAT LOG ANALYSIS REPORT\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Summary
                summary = self.analysis_results.get('summary', {})
                f.write("📊 SUMMARY\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Log Entries: {summary.get('total_entries', 0):,}\n")
                f.write(f"Unique Source IPs: {summary.get('unique_source_ips', 0):,}\n")
                f.write(f"Unique Destination IPs: {summary.get('unique_destination_ips', 0):,}\n")
                
                action_counts = summary.get('action_counts', {})
                for action, count in action_counts.items():
                    f.write(f"{action.title()} Actions: {count:,}\n")
                f.write("\n")
                
                # Security Alerts
                alerts = self.analysis_results.get('security_alerts', [])
                if alerts:
                    f.write("🚨 SECURITY ALERTS\n")
                    f.write("-" * 20 + "\n")
                    for alert in alerts:
                        f.write(f"[{alert['level']}] {alert['message']}\n")
                    f.write("\n")
                
                # Threat Detections
                threats = self.analysis_results.get('threat_detections', [])
                if threats:
                    f.write("🔍 THREAT DETECTIONS\n")
                    f.write("-" * 20 + "\n")
                    for threat in threats:
                        f.write(f"• {threat['type']} ({threat['severity']} Priority)\n")
                        f.write(f"  Count: {threat['count']}\n")
                        f.write(f"  Description: {threat['description']}\n\n")
                
                # Top Statistics
                stats = self.analysis_results.get('top_statistics', {})
                if stats.get('top_denied_ips'):
                    f.write("🚫 TOP DENIED SOURCE IPs\n")
                    f.write("-" * 20 + "\n")
                    for ip, count in list(stats['top_denied_ips'].items())[:5]:
                        f.write(f"{ip}: {count} denials\n")
                    f.write("\n")
                
                # Recommendations
                recommendations = self.analysis_results.get('recommendations', [])
                if recommendations:
                    f.write("💡 RECOMMENDATIONS\n")
                    f.write("-" * 20 + "\n")
                    for i, rec in enumerate(recommendations, 1):
                        f.write(f"{i}. {rec}\n")
                
            return True
            
        except Exception as e:
            print(f"Error exporting report: {e}")
            return False
    
    def export_csv_summary(self, output_path: str) -> bool:
        """
        Export analysis summary to CSV format.
        
        Args:
            output_path: Path for output CSV file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Header
                writer.writerow(['Firewall Threat Analysis Summary'])
                writer.writerow(['Generated', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                writer.writerow([])
                
                # Summary stats
                summary = self.analysis_results.get('summary', {})
                writer.writerow(['Summary Statistics'])
                writer.writerow(['Metric', 'Value'])
                writer.writerow(['Total Entries', summary.get('total_entries', 0)])
                writer.writerow(['Unique Source IPs', summary.get('unique_source_ips', 0)])
                writer.writerow(['Unique Destination IPs', summary.get('unique_destination_ips', 0)])
                writer.writerow([])
                
                # Threat detections
                threats = self.analysis_results.get('threat_detections', [])
                if threats:
                    writer.writerow(['Threat Detections'])
                    writer.writerow(['Type', 'Severity', 'Count', 'Description'])
                    for threat in threats:
                        writer.writerow([
                            threat['type'],
                            threat['severity'],
                            threat['count'],
                            threat['description']
                        ])
                    writer.writerow([])
                
                # Top denied IPs
                stats = self.analysis_results.get('top_statistics', {})
                if stats.get('top_denied_ips'):
                    writer.writerow(['Top Denied Source IPs'])
                    writer.writerow(['IP Address', 'Denial Count'])
                    for ip, count in stats['top_denied_ips'].items():
                        writer.writerow([ip, count])
                
            return True
            
        except Exception as e:
            print(f"Error exporting CSV: {e}")
            return False
