#!/usr/bin/env python3
"""
Test script to verify the awareness form fields are correctly configured
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add current directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modern_ui import ResponsiveManager, StyleManager
from awareness_forms import AwarenessActivityForm

def test_awareness_form():
    """Test the awareness form to verify all fields are present."""
    
    # Create test window
    root = tk.Tk()
    root.title("Test Awareness Form")
    root.geometry("800x600")
    
    # Initialize style and responsive managers
    style_manager = StyleManager(root)
    responsive_manager = ResponsiveManager(root)
    
    # Create test frame
    test_frame = ttk.Frame(root, style='Modern.TFrame')
    test_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    test_frame.columnconfigure(0, weight=1)
    test_frame.rowconfigure(0, weight=1)
    
    def dummy_callback(data):
        """Dummy callback for form submission."""
        print("Form submitted with data:")
        for key, value in data.items():
            print(f"  {key}: {value}")
    
    # Create awareness form
    try:
        awareness_form = AwarenessActivityForm(
            test_frame, 
            dummy_callback, 
            responsive_manager
        )
        
        print("✅ Awareness form created successfully!")
        print("📋 Form fields available:")
        
        # Check if all required fields exist
        fields_to_check = [
            ('topic_subject_field', 'Title/Subject'),
            ('activity_date_field', 'Date'),
            ('number_of_pax_field', 'Number of Participants'),
            ('pretest_result_field', 'Pre-test Percentage'),
            ('posttest_result_field', 'Post-test Percentage'),
            ('remarks_text', 'Remarks')
        ]
        
        for field_attr, field_name in fields_to_check:
            if hasattr(awareness_form, field_attr):
                print(f"  ✅ {field_name} field: Present")
            else:
                print(f"  ❌ {field_name} field: Missing")
        
        # Test responsive manager
        if hasattr(awareness_form, 'responsive_manager') and awareness_form.responsive_manager:
            print(f"✅ Responsive manager: Available (current breakpoint: {awareness_form.responsive_manager.current_breakpoint})")
        else:
            print("❌ Responsive manager: Not available")
        
        print("\n🎯 Test completed! Close the window to exit.")
        
        # Start the GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error creating awareness form: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_awareness_form()
