"""
SSL Table View module for displaying and managing SSL records with modern UI.
Handles SSL record display, filtering, and SSL scanning functionality.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Callable, Optional

from utils import show_confirmation_dialog, show_error_message, show_success_message
from modern_ui import ModernCard, ModernButton, ModernTheme, get_responsive_padding
from ssl_checker import <PERSON><PERSON>he<PERSON>, SSLValidator, AsyncSSLChecker


class SSLRecordTable:
    """Table widget for displaying and managing SSL records with modern UI."""

    def __init__(self, parent: tk.Widget, on_edit_callback: Callable[[Dict], None],
                 on_delete_callback: Callable[[int], None], on_update_ssl_callback: Callable[[int, Dict], None] = None,
                 responsive_manager=None):
        """
        Initialize the SSL record table.

        Args:
            parent: Parent widget
            on_edit_callback: Callback function for editing records
            on_delete_callback: Callback function for deleting records
            on_update_ssl_callback: Callback function for updating SSL records after scan
            responsive_manager: Responsive manager for layout adjustments
        """
        self.parent = parent
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        self.on_update_ssl_callback = on_update_ssl_callback
        self.responsive_manager = responsive_manager
        self.records = []
        self.current_breakpoint = 'lg'

        # SSL checker instances
        self.ssl_checker = SSLChecker()
        self.async_ssl_checker = AsyncSSLChecker(callback=self.on_ssl_scan_complete)

        self.create_table()

        # Register for responsive updates
        if self.responsive_manager:
            self.responsive_manager.add_resize_callback(self.on_breakpoint_change)

    def create_table(self) -> None:
        """Create the modern table UI elements."""
        # Configure parent grid
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(1, weight=1)

        # Main frame
        self.main_frame = ttk.Frame(self.parent, style='Modern.TFrame')
        self.main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                           padx=get_responsive_padding(self.current_breakpoint),
                           pady=get_responsive_padding(self.current_breakpoint))
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(0, weight=1)

        # Table card
        self.table_card = ModernCard(self.main_frame, title="🔒 SSL Records")
        self.table_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Table content frame
        self.table_content = ttk.Frame(self.table_card, style='Surface.TFrame')
        self.table_card.add_content(self.table_content)
        self.table_content.columnconfigure(0, weight=1)
        self.table_content.rowconfigure(0, weight=1)

        # Create treeview with scrollbars
        self.create_treeview()

        # Create action buttons
        self.create_action_buttons()

    def on_breakpoint_change(self, breakpoint: str) -> None:
        """Handle responsive breakpoint changes."""
        if breakpoint != self.current_breakpoint:
            self.current_breakpoint = breakpoint
            # Update button layout
            self.update_button_layout()
            # Recreate table with responsive columns
            self.recreate_table_responsive()

    def create_treeview(self) -> None:
        """Create the modern treeview widget with scrollbars."""
        # Frame for treeview and scrollbars
        tree_frame = ttk.Frame(self.table_content, style='Surface.TFrame')
        tree_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Define columns (responsive)
        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: show only essential columns
            columns = ('ID', 'Application Name', 'SSL Status', 'Expiration Status')
        elif self.current_breakpoint == 'md':
            # Tablet: show more columns
            columns = ('ID', 'Application Name', 'IP Address', 'SSL Status', 'SSL Expiration', 'Expiration Status')
        else:
            # Desktop: show all columns
            columns = (
                'ID', 'Application Name', 'IP Address', 'URL', 'SSL Status',
                'SSL Expiration', 'Expiration Status', 'Last Scan', 'Remarks'
            )

        # Create treeview with modern styling
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                               height=15, style='Modern.Treeview')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure column headings and widths (responsive)
        self.configure_columns(columns)

        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # Bind double-click event for editing
        self.tree.bind('<Double-1>', self.on_double_click)

    def configure_columns(self, columns: tuple) -> None:
        """Configure column headings and widths based on current breakpoint."""
        # Base column widths
        base_widths = {
            'ID': 50,
            'Application Name': 150,
            'IP Address': 120,
            'URL': 200,
            'SSL Status': 100,
            'SSL Expiration': 100,
            'Expiration Status': 120,
            'Last Scan': 120,
            'Remarks': 200
        }

        # Adjust widths based on breakpoint
        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: wider columns for fewer columns
            width_multiplier = 1.5
        elif self.current_breakpoint == 'md':
            # Tablet: slightly wider
            width_multiplier = 1.2
        else:
            # Desktop: normal widths
            width_multiplier = 1.0

        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            width = int(base_widths.get(col, 100) * width_multiplier)
            self.tree.column(col, width=width, minwidth=50)

    def create_action_buttons(self) -> None:
        """Create modern action buttons for the table."""
        self.button_frame = ttk.Frame(self.table_content, style='Surface.TFrame')
        self.button_frame.grid(row=1, column=0, pady=(ModernTheme.PADDING_MD, 0))

        self.update_button_layout()

    def update_button_layout(self) -> None:
        """Update button layout based on current breakpoint."""
        # Clear existing buttons
        for widget in self.button_frame.winfo_children():
            widget.destroy()

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.scan_button = ModernButton(self.button_frame, variant="success",
                                          text="🔍 Scan SSL", command=self.scan_selected_ssl)
            self.scan_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.scan_all_button = ModernButton(self.button_frame, variant="warning",
                                              text="🔍 Scan All SSL", command=self.scan_all_ssl)
            self.scan_all_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.edit_button = ModernButton(self.button_frame, variant="primary",
                                          text="Edit Selected", command=self.edit_selected)
            self.edit_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.delete_button = ModernButton(self.button_frame, variant="error",
                                            text="Delete Selected", command=self.delete_selected)
            self.delete_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.view_file_button = ModernButton(self.button_frame, variant="secondary",
                                               text="👁️ View File", command=self.view_file_selected)
            self.view_file_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.export_history_button = ModernButton(self.button_frame, variant="warning",
                                                     text="📊 Export Scan History", command=self.export_scan_history)
            self.export_history_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.refresh_button = ModernButton(self.button_frame, variant="secondary",
                                             text="Refresh", command=self.refresh_table)
            self.refresh_button.pack(fill=tk.X)
        else:
            # Desktop: horizontal layout
            self.scan_button = ModernButton(self.button_frame, variant="success",
                                          text="🔍 Scan SSL", command=self.scan_selected_ssl)
            self.scan_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.scan_all_button = ModernButton(self.button_frame, variant="warning",
                                              text="🔍 Scan All SSL", command=self.scan_all_ssl)
            self.scan_all_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.edit_button = ModernButton(self.button_frame, variant="primary",
                                          text="Edit Selected", command=self.edit_selected)
            self.edit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.delete_button = ModernButton(self.button_frame, variant="error",
                                            text="Delete Selected", command=self.delete_selected)
            self.delete_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.view_file_button = ModernButton(self.button_frame, variant="secondary",
                                               text="👁️ View File", command=self.view_file_selected)
            self.view_file_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.export_history_button = ModernButton(self.button_frame, variant="warning",
                                                     text="📊 Export History", command=self.export_scan_history)
            self.export_history_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.refresh_button = ModernButton(self.button_frame, variant="secondary",
                                             text="Refresh", command=self.refresh_table)
            self.refresh_button.pack(side=tk.LEFT)

    def load_data(self, records: List[Dict]) -> None:
        """
        Load data into the table.

        Args:
            records: List of dictionaries containing SSL record data
        """
        self.records = records
        self.refresh_display()

        # Check for expired SSL records and show notification
        self.check_and_notify_expired_ssl_records()

    def refresh_display(self) -> None:
        """Refresh the table display with current data."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get current columns
        columns = self.tree['columns']

        # Insert new data with responsive column selection and SSL expiration highlighting
        for record in self.records:
            values = self.get_record_values(record, columns)
            item = self.tree.insert('', tk.END, values=values)

            # Check for different types of SSL expiration status
            if SSLValidator.is_ssl_record_expired(record):
                # Records with expiration_status="No" but past SSL expiration date (red highlighting)
                try:
                    self.tree.item(item, tags=('ssl_expired',))
                except:
                    pass  # Fallback if styling not supported
            elif SSLValidator.is_ssl_record_manually_expired(record):
                # Records with expiration_status="Yes" (yellow highlighting)
                try:
                    self.tree.item(item, tags=('ssl_manually_expired',))
                except:
                    pass  # Fallback if styling not supported

        # Configure tag styling for different SSL expiration states
        try:
            # Red background for automatically detected expired SSL records
            self.tree.tag_configure('ssl_expired', background='#ffebee', foreground='#c62828')
            # Yellow background for manually marked expired SSL records
            self.tree.tag_configure('ssl_manually_expired', background='#ffc107', foreground='#000000')
        except:
            pass  # Fallback if styling not supported

    def get_record_values(self, record: Dict, columns: tuple) -> tuple:
        """Get record values for the specified columns."""
        def safe_get(key, default=''):
            """Safely get value from record, handling None values."""
            value = record.get(key, default)
            return str(value) if value is not None else default

        value_map = {
            'ID': safe_get('id'),
            'Application Name': safe_get('application_name'),
            'IP Address': safe_get('ip_address'),
            'URL': safe_get('ip_url'),
            'SSL Status': safe_get('ssl_valid', 'Unknown'),
            'SSL Expiration': safe_get('ssl_expiration_date'),
            'Expiration Status': safe_get('expiration_status', 'No'),
            'Last Scan': safe_get('last_scan_date'),
            'Remarks': safe_get('remarks')
        }

        return tuple(value_map.get(col, '') for col in columns)

    def recreate_table_responsive(self) -> None:
        """Recreate table with responsive column layout."""
        # Store current data
        current_records = self.records

        # Destroy and recreate treeview
        if hasattr(self, 'tree'):
            self.tree.destroy()

        self.create_treeview()

        # Reload data
        self.records = current_records
        self.refresh_display()

    def sort_by_column(self, column: str) -> None:
        """
        Sort table data by specified column.

        Args:
            column: Column name to sort by
        """
        # Get column index
        columns = list(self.tree['columns'])
        if column not in columns:
            return

        col_index = columns.index(column)

        # Get current data
        data = []
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            data.append(values)

        # Sort data
        reverse = getattr(self, f'_sort_{column}_reverse', False)

        if column in ['ID']:
            # Numeric sort
            data.sort(key=lambda x: int(x[col_index]) if x[col_index] and str(x[col_index]).isdigit() else 0, reverse=reverse)
        elif column in ['SSL Expiration', 'Last Scan']:
            # Date sort
            data.sort(key=lambda x: x[col_index] if x[col_index] else '', reverse=reverse)
        else:
            # String sort
            data.sort(key=lambda x: str(x[col_index]).lower(), reverse=reverse)

        # Update sort direction for next click
        setattr(self, f'_sort_{column}_reverse', not reverse)

        # Clear and repopulate tree
        for item in self.tree.get_children():
            self.tree.delete(item)

        for values in data:
            self.tree.insert('', tk.END, values=values)

    def get_selected_record(self) -> Optional[Dict]:
        """
        Get the currently selected record.

        Returns:
            Dictionary containing selected record data or None if no selection
        """
        selection = self.tree.selection()
        if not selection:
            return None

        item = selection[0]
        values = self.tree.item(item)['values']

        if not values:
            return None

        # Find the record by ID
        record_id = int(values[0]) if values[0] else None
        if record_id:
            for record in self.records:
                if record.get('id') == record_id:
                    return record

        return None

    def on_double_click(self, event=None) -> None:
        """Handle double-click event on table row."""
        self.edit_selected()

    def edit_selected(self) -> None:
        """Edit the selected record."""
        record = self.get_selected_record()
        if record:
            self.on_edit_callback(record)
            self.refresh_table() # Refresh table after edit 06-09-2025
        else:
            show_error_message("No Selection", "Please select a record to edit.")

    def delete_selected(self) -> None:
        """Delete the selected record."""
        record = self.get_selected_record()
        if not record:
            show_error_message("No Selection", "Please select a record to delete.")
            return

        # Confirm deletion
        application_name = record.get('application_name', 'Unknown')
        if show_confirmation_dialog(
            "Confirm Deletion",
            f"Are you sure you want to delete the SSL record for {application_name}?"
        ):
            try:
                self.on_delete_callback(record['id'])
                show_success_message("Success", "SSL record deleted successfully!")
                self.refresh_table()
            except Exception as e:
                show_error_message("Error", f"Failed to delete SSL record: {e}")

    def view_file_selected(self) -> None:
        """View the verification file for the selected SSL record."""
        record = self.get_selected_record()
        if not record:
            show_error_message("No Selection", "Please select a record to view its file.")
            return

        file_path = record.get('verification_file_path', '')
        if not file_path or not file_path.strip():
            show_error_message("No File", "The selected SSL record does not have a verification file.")
            return

        # Import FileHandler here to avoid circular imports
        from utils import FileHandler
        if not FileHandler.view_file(file_path):
            show_error_message("View Error", "Failed to open the verification file.")
        else:
            # File opened successfully
            pass

    def refresh_table(self) -> None:
        """Refresh the table by reloading data."""
        # This will be implemented by the main application
        pass

    def export_scan_history(self) -> None:
        """Export SSL scan history to CSV file."""
        try:
            # Import here to avoid circular imports
            from utils import save_file_dialog, show_error_message, show_success_message

            # Get scan history data (only records that have been scanned)
            scan_history = [record for record in self.records if record.get('last_scan_date')]

            if not scan_history:
                show_error_message("No Scan History",
                                 "No SSL scan history data to export. Please perform SSL scans first.")
                return

            filename = save_file_dialog("Export SSL Scan History to CSV", ".csv")
            if filename:
                from utils import FileHandler
                if FileHandler.export_ssl_scan_history_to_csv(scan_history, filename, include_statistics=True):
                    show_success_message("Export Success",
                                       f"SSL scan history exported to {filename}\n\n"
                                       f"Exported {len(scan_history)} scan records with statistics.")
                else:
                    show_error_message("Export Error", "Failed to export SSL scan history to CSV")

        except Exception as e:
            from utils import show_error_message
            show_error_message("Export Error", f"Failed to export SSL scan history: {e}")

    def scan_selected_ssl(self) -> None:
        """Scan SSL certificate for the selected record."""
        record = self.get_selected_record()
        if not record:
            show_error_message("No Selection", "Please select a record to scan SSL.")
            return

        self.scan_ssl_record(record)

    def scan_all_ssl(self) -> None:
        """Scan SSL certificates for all records."""
        if not self.records:
            show_error_message("No Records", "No SSL records to scan.")
            return

        if show_confirmation_dialog(
            "Scan All SSL",
            f"Are you sure you want to scan SSL certificates for all {len(self.records)} records?"
        ):
            for record in self.records:
                self.scan_ssl_record(record)

    def scan_ssl_record(self, record: Dict) -> None:
        """
        Scan SSL certificate for a specific record.

        Args:
            record: SSL record to scan
        """
        try:
            url = record.get('ip_url', '')
            if not url:
                show_error_message("Invalid URL", f"No URL found for {record.get('application_name', 'Unknown')}")
                return

            # Use async SSL checker for non-blocking scan
            self.async_ssl_checker.scan_url_async(url, record)

            # Show scanning message
            messagebox.showinfo("SSL Scan", f"Scanning SSL certificate for {record.get('application_name', 'Unknown')}...")

        except Exception as e:
            show_error_message("Scan Error", f"Failed to scan SSL: {e}")

    def on_ssl_scan_complete(self, record_data: Dict, ssl_info: Dict) -> None:
        """
        Callback for when SSL scan is complete.

        Args:
            record_data: Original record data
            ssl_info: SSL scan results
        """
        try:
            # Update record with SSL information
            record_data.update(ssl_info)

            # Save the updated SSL information to database
            if hasattr(self, 'on_update_ssl_callback') and self.on_update_ssl_callback:
                self.on_update_ssl_callback(record_data['id'], record_data)

            # Check if SSL is expired and show notification
            if ssl_info.get('ssl_valid') == 'Expired' or SSLValidator.is_ssl_record_expired(record_data):
                messagebox.showwarning(
                    "SSL Expired",
                    f"⚠️ SSL certificate for {record_data.get('application_name', 'Unknown')} has expired!\n\n"
                    f"Expiration Date: {ssl_info.get('ssl_expiration_date', 'Unknown')}"
                )
            elif ssl_info.get('error'):
                show_error_message(
                    "SSL Scan Error",
                    f"Failed to scan SSL for {record_data.get('application_name', 'Unknown')}:\n{ssl_info.get('error')}"
                )
            else:
                show_success_message(
                    "SSL Scan Complete",
                    f"SSL scan completed for {record_data.get('application_name', 'Unknown')}\n"
                    f"Status: {ssl_info.get('ssl_valid', 'Unknown')}\n"
                    f"Expiration: {ssl_info.get('ssl_expiration_date', 'Unknown')}"
                )

            # Refresh display to show updated SSL information
            self.refresh_display()

        except Exception as e:
            show_error_message("Update Error", f"Failed to update SSL information: {e}")

    def filter_data(self, filters: Dict) -> None:
        """
        Filter table data based on provided criteria.

        Args:
            filters: Dictionary containing filter criteria
        """
        if not filters:
            self.refresh_display()
            return

        filtered_records = []
        for record in self.records:
            match = True

            # Check application name filter (partial match)
            if filters.get('application_name'):
                name_filter = filters['application_name'].lower()
                record_name = record.get('application_name', '').lower()
                if name_filter not in record_name:
                    match = False

            if match:
                filtered_records.append(record)

        # Temporarily store original records and display filtered data
        original_records = self.records
        self.records = filtered_records
        self.refresh_display()
        self.records = original_records  # Restore original data

    def get_record_count(self) -> int:
        """
        Get the number of records currently displayed.

        Returns:
            Number of records in the table
        """
        return len(self.tree.get_children())

    def clear_selection(self) -> None:
        """Clear the current selection."""
        for item in self.tree.selection():
            self.tree.selection_remove(item)

    def check_and_notify_expired_ssl_records(self) -> None:
        """Check for expired SSL records and show notification if any are found."""
        expired_records = SSLValidator.get_expired_ssl_records(self.records)
        if expired_records:
            count = len(expired_records)
            if count == 1:
                record = expired_records[0]
                message = (f"⚠️ Expired SSL Certificate Detected!\n\n"
                          f"Application: {record.get('application_name', 'Unknown')}\n"
                          f"URL: {record.get('ip_url', 'Unknown')}\n"
                          f"SSL Expiration: {record.get('ssl_expiration_date', 'Unknown')}\n"
                          f"Current Status: Not Expired\n\n"
                          f"This SSL certificate has expired but is still marked as 'Not Expired'. "
                          f"Please review and update the status.")
            else:
                message = (f"⚠️ {count} Expired SSL Certificates Detected!\n\n"
                          f"Multiple SSL certificates have expired "
                          f"but are still marked as 'Not Expired'.\n\n"
                          f"Please review and update the expiration status for these records.")

            messagebox.showwarning("SSL Expiration Alert", message)
