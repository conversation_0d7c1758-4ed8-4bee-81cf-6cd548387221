"""
Table view module for Firewall Social Media Access application.
Handles the display and management of access request records in a table format with modern UI.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Dict, Callable, Optional
from utils import show_confirmation_dialog, show_error_message, show_success_message
from modern_ui import ModernCard, ModernButton, ModernTheme, get_responsive_padding, ExpirationValidator


class AccessRequestTable:
    """Table widget for displaying and managing access request records with modern UI."""

    def __init__(self, parent: tk.Widget, on_edit_callback: Callable[[Dict], None],
                 on_delete_callback: Callable[[int], None], responsive_manager=None):
        """
        Initialize the access request table.

        Args:
            parent: Parent widget
            on_edit_callback: Callback function for editing records
            on_delete_callback: Callback function for deleting records
            responsive_manager: Responsive manager for layout adjustments
        """
        self.parent = parent
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        self.responsive_manager = responsive_manager
        self.records = []
        self.current_breakpoint = 'lg'

        self.create_table()

        # Register for responsive updates
        if self.responsive_manager:
            self.responsive_manager.add_resize_callback(self.on_breakpoint_change)

    def create_table(self) -> None:
        """Create the modern table UI elements."""
        # Configure parent grid
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(1, weight=1)

        # Main frame
        self.main_frame = ttk.Frame(self.parent, style='Modern.TFrame')
        self.main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                           padx=get_responsive_padding(self.current_breakpoint),
                           pady=get_responsive_padding(self.current_breakpoint))
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(0, weight=1)

        # Table card
        # Dynamic title based on screen size
        title = "Access Request Records" if self.responsive_manager.current_breakpoint not in ['xs', 'sm'] else "Access Requests"
        self.table_card = ModernCard(self.main_frame, title=title)
        self.table_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Table content frame
        self.table_content = ttk.Frame(self.table_card, style='Surface.TFrame')
        self.table_card.add_content(self.table_content)
        self.table_content.columnconfigure(0, weight=1)
        self.table_content.rowconfigure(0, weight=1)

        # Create treeview with scrollbars
        self.create_treeview()

        # Create action buttons
        self.create_action_buttons()

    def on_breakpoint_change(self, breakpoint: str) -> None:
        """Handle responsive breakpoint changes."""
        if breakpoint != self.current_breakpoint:
            self.current_breakpoint = breakpoint
            # Update button layout
            self.update_button_layout()
            # Recreate table with responsive columns
            self.recreate_table_responsive()

    def create_treeview(self) -> None:
        """Create the modern treeview widget with scrollbars."""
        # Frame for treeview and scrollbars
        tree_frame = ttk.Frame(self.table_content, style='Surface.TFrame')
        tree_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Define columns (responsive)
        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: show only essential columns
            columns = ('ID', 'Employee Name', 'IP Address', 'Request Type', 'Is Expired')
        elif self.current_breakpoint == 'md':
            # Tablet: show more columns
            columns = ('ID', 'IP Address', 'Employee Name', 'Section', 'Date Requested', 'Request Type', 'Is Expired')
        else:
            # Desktop: show all columns
            columns = (
                'ID', 'IP Address', 'Employee Name', 'Section', 'Position',
                'Date Requested', 'Request Type', 'Is Expired', 'Date Expiration',
                'Is IPv4 Reserved', 'Remarks'
            )

        # Create treeview with modern styling
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                               height=15, style='Modern.Treeview')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure column headings and widths (responsive)
        self.configure_columns(columns)

        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # Bind double-click event for editing
        self.tree.bind('<Double-1>', self.on_double_click)

    def configure_columns(self, columns: tuple) -> None:
        """Configure column headings and widths based on current breakpoint."""
        # Base column widths
        base_widths = {
            'ID': 50,
            'IP Address': 120,
            'Employee Name': 150,
            'Section': 100,
            'Position': 100,
            'Date Requested': 100,
            'Request Type': 100,
            'Is Expired': 80,
            'Date Expiration': 100,
            'Is IPv4 Reserved': 100,
            'Remarks': 200
        }

        # Adjust widths based on breakpoint
        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: wider columns for fewer columns
            width_multiplier = 1.5
        elif self.current_breakpoint == 'md':
            # Tablet: slightly wider
            width_multiplier = 1.2
        else:
            # Desktop: normal widths
            width_multiplier = 1.0

        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            width = int(base_widths.get(col, 100) * width_multiplier)
            self.tree.column(col, width=width, minwidth=50)

    def create_action_buttons(self) -> None:
        """Create modern action buttons for the table."""
        self.button_frame = ttk.Frame(self.table_content, style='Surface.TFrame')
        self.button_frame.grid(row=1, column=0, pady=(ModernTheme.PADDING_MD, 0))

        self.update_button_layout()

    def update_button_layout(self) -> None:
        """Update button layout based on current breakpoint."""
        # Clear existing buttons
        for widget in self.button_frame.winfo_children():
            widget.destroy()

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.edit_button = ModernButton(self.button_frame, variant="primary", text="Edit Selected", command=self.edit_selected)
            self.edit_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.delete_button = ModernButton(self.button_frame, variant="error", text="Delete Selected", command=self.delete_selected)
            self.delete_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.view_file_button = ModernButton(self.button_frame, variant="secondary", text="👁️ View File", command=self.view_file_selected)
            self.view_file_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.refresh_button = ModernButton(self.button_frame, variant="secondary", text="Refresh", command=self.refresh_table)
            self.refresh_button.pack(fill=tk.X)
        else:
            # Desktop: horizontal layout
            self.edit_button = ModernButton(self.button_frame, variant="primary", text="Edit Selected", command=self.edit_selected)
            self.edit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.delete_button = ModernButton(self.button_frame, variant="error", text="Delete Selected", command=self.delete_selected)
            self.delete_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.view_file_button = ModernButton(self.button_frame, variant="secondary", text="👁️ View File", command=self.view_file_selected)
            self.view_file_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.refresh_button = ModernButton(self.button_frame, variant="secondary", text="Refresh", command=self.refresh_table)
            self.refresh_button.pack(side=tk.LEFT)

    def load_data(self, records: List[Dict]) -> None:
        """
        Load data into the table.

        Args:
            records: List of dictionaries containing record data
        """
        self.records = records
        self.refresh_display()

        # Check for expired records and show notification
        self.check_and_notify_expired_records()

    def refresh_display(self) -> None:
        """Refresh the table display with current data."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get current columns
        columns = self.tree['columns']

        # Insert new data with responsive column selection and expiration highlighting
        for record in self.records:
            values = self.get_record_values(record, columns)
            item = self.tree.insert('', tk.END, values=values)

            # Check for different types of expiration status
            if ExpirationValidator.is_record_expired(record):
                # Records with is_expired="No" but past expiration date (red highlighting)
                self.tree.set(item, 'Is Expired', '⚠️ EXPIRED')
                try:
                    self.tree.item(item, tags=('expired',))
                except:
                    pass  # Fallback if styling not supported
            elif ExpirationValidator.is_record_manually_expired(record):
                # Records with is_expired="Yes" (yellow highlighting)
                try:
                    self.tree.item(item, tags=('manually_expired',))
                except:
                    pass  # Fallback if styling not supported

        # Configure tag styling for different expiration states
        try:
            # Red background for automatically detected expired records
            self.tree.tag_configure('expired', background='#ffebee', foreground='#c62828')
            # Yellow background for manually marked expired records
            self.tree.tag_configure('manually_expired', background='#ffc107', foreground='#000000')
        except:
            pass  # Fallback if styling not supported

    def get_record_values(self, record: Dict, columns: tuple) -> tuple:
        """Get record values for the specified columns."""
        value_map = {
            'ID': record.get('id', ''),
            'IP Address': record.get('ip_address', ''),
            'Employee Name': record.get('employee_name', ''),
            'Section': record.get('section', ''),
            'Position': record.get('position', ''),
            'Date Requested': record.get('date_requested', ''),
            'Request Type': record.get('request_type', ''),
            'Is Expired': record.get('is_expired', ''),
            'Date Expiration': record.get('date_expiration', '') or '',
            'Is IPR Reserved': record.get('is_ipr_reserved', ''),
            'Remarks': record.get('remarks', '') or ''
        }

        return tuple(value_map.get(col, '') for col in columns)

    def recreate_table_responsive(self) -> None:
        """Recreate table with responsive column layout."""
        # Store current data
        current_records = self.records

        # Destroy and recreate treeview
        if hasattr(self, 'tree'):
            self.tree.destroy()

        self.create_treeview()

        # Reload data
        self.records = current_records
        self.refresh_display()

    def sort_by_column(self, column: str) -> None:
        """
        Sort table data by specified column.

        Args:
            column: Column name to sort by
        """
        # Get column index
        columns = list(self.tree['columns'])
        if column not in columns:
            return

        col_index = columns.index(column)

        # Get current data
        data = []
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            data.append(values)

        # Sort data
        reverse = getattr(self, f'_sort_{column}_reverse', False)

        if column in ['ID']:
            # Numeric sort
            data.sort(key=lambda x: int(x[col_index]) if x[col_index] and str(x[col_index]).isdigit() else 0, reverse=reverse)
        elif column in ['Date Requested', 'Date Expiration']:
            # Date sort
            data.sort(key=lambda x: x[col_index] if x[col_index] else '', reverse=reverse)
        else:
            # String sort
            data.sort(key=lambda x: str(x[col_index]).lower(), reverse=reverse)

        # Update sort direction for next click
        setattr(self, f'_sort_{column}_reverse', not reverse)

        # Clear and repopulate tree
        for item in self.tree.get_children():
            self.tree.delete(item)

        for values in data:
            self.tree.insert('', tk.END, values=values)

    def get_selected_record(self) -> Optional[Dict]:
        """
        Get the currently selected record.

        Returns:
            Dictionary containing selected record data or None if no selection
        """
        selection = self.tree.selection()
        if not selection:
            return None

        item = selection[0]
        values = self.tree.item(item)['values']

        if not values:
            return None

        # Find the record by ID
        record_id = int(values[0]) if values[0] else None
        if record_id:
            for record in self.records:
                if record.get('id') == record_id:
                    return record

        return None

    def on_double_click(self, event=None) -> None:
        """Handle double-click event on table row."""
        self.edit_selected()

    def edit_selected(self) -> None:
        """Edit the selected record."""
        record = self.get_selected_record()
        if record:
            self.on_edit_callback(record)
            self.refresh_table() # Refresh table after edit 06-09-2025
        else:
            show_error_message("No Selection", "Please select a record to edit.")

    def delete_selected(self) -> None:
        """Delete the selected record."""
        record = self.get_selected_record()
        if not record:
            show_error_message("No Selection", "Please select a record to delete.")
            return

        # Confirm deletion
        employee_name = record.get('employee_name', 'Unknown')
        if show_confirmation_dialog(
            "Confirm Deletion",
            f"Are you sure you want to delete the access request for {employee_name}?"
        ):
            try:
                self.on_delete_callback(record['id'])
                show_success_message("Success", "Record deleted successfully!")
                self.refresh_table()
            except Exception as e:
                show_error_message("Error", f"Failed to delete record: {e}")

    def view_file_selected(self) -> None:
        """View the verification file for the selected record."""
        record = self.get_selected_record()
        if not record:
            show_error_message("No Selection", "Please select a record to view its file.")
            return

        file_path = record.get('verification_file_path', '')
        if not file_path or not file_path.strip():
            show_error_message("No File", "The selected record does not have a verification file.")
            return

        # Import FileHandler here to avoid circular imports
        from utils import FileHandler
        if not FileHandler.view_file(file_path):
            show_error_message("View Error", "Failed to open the verification file.")
        else:
            # File opened successfully
            pass

    def refresh_table(self) -> None:
        """Refresh the table by reloading data."""
        # This will be implemented by the main application
        pass

    def filter_data(self, filters: Dict) -> None:
        """
        Filter table data based on provided criteria.

        Args:
            filters: Dictionary containing filter criteria
        """
        if not filters:
            self.refresh_display()
            return

        filtered_records = []
        for record in self.records:
            match = True

            # Check date filter
            if filters.get('date_requested') and record.get('date_requested') != filters['date_requested']:
                match = False

            # Check IP address filter (partial match)
            if filters.get('ip_address'):
                ip_filter = filters['ip_address'].lower()
                record_ip = record.get('ip_address', '').lower()
                if ip_filter not in record_ip:
                    match = False

            # Check employee name filter (partial match)
            if filters.get('employee_name'):
                name_filter = filters['employee_name'].lower()
                record_name = record.get('employee_name', '').lower()
                if name_filter not in record_name:
                    match = False

            if match:
                filtered_records.append(record)

        # Temporarily store original records and display filtered data
        original_records = self.records
        self.records = filtered_records
        self.refresh_display()
        self.records = original_records  # Restore original data

    def get_record_count(self) -> int:
        """
        Get the number of records currently displayed.

        Returns:
            Number of records in the table
        """
        return len(self.tree.get_children())

    def clear_selection(self) -> None:
        """Clear the current selection."""
        for item in self.tree.selection():
            self.tree.selection_remove(item)

    def check_and_notify_expired_records(self) -> None:
        """Check for expired records and show notification if any are found."""
        expired_records = ExpirationValidator.get_expired_records(self.records)
        if expired_records:
            ExpirationValidator.show_expiration_notification(expired_records)
