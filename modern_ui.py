"""
Modern UI styling and responsive design module for Firewall Social Media Access application.
Provides web-like interface components and responsive layout management.
"""

import tkinter as tk
from tkinter import ttk, font, messagebox
from typing import Callable, List, Dict
from datetime import datetime, date


class ModernTheme:
    """Modern color theme and styling constants."""

    # Color palette - Modern web-like colors
    PRIMARY = "#2563eb"      # Blue
    PRIMARY_DARK = "#1d4ed8"
    PRIMARY_LIGHT = "#3b82f6"

    SECONDARY = "#64748b"    # Slate
    SECONDARY_LIGHT = "#94a3b8"
    SECONDARY_DARK = "#475569"

    SUCCESS = "#10b981"      # Green
    WARNING = "#f59e0b"      # Amber
    ERROR = "#ef4444"        # Red

    BACKGROUND = "#f8fafc"   # Very light gray
    SURFACE = "#ffffff"      # White
    SURFACE_VARIANT = "#f1f5f9"  # Light gray

    TEXT_PRIMARY = "#0f172a"     # Very dark
    TEXT_SECONDARY = "#64748b"   # Medium gray
    TEXT_MUTED = "#94a3b8"       # Light gray

    BORDER = "#e2e8f0"       # Light border
    BORDER_FOCUS = "#3b82f6"  # Blue border for focus

    SHADOW = "#00000010"     # Light shadow

    # Spacing
    PADDING_XS = 4
    PADDING_SM = 8
    PADDING_MD = 16
    PADDING_LG = 24
    PADDING_XL = 32

    # Border radius (simulated with relief and padding)
    BORDER_RADIUS = 6

    # Font sizes
    FONT_XS = 10
    FONT_SM = 11
    FONT_MD = 12
    FONT_LG = 14
    FONT_XL = 16
    FONT_2XL = 20
    FONT_3XL = 24


class ResponsiveManager:
    """Manages responsive behavior for the application."""

    def __init__(self, root: tk.Tk):
        """
        Initialize responsive manager.

        Args:
            root: Root window
        """
        self.root = root
        self.breakpoints = {
            'xs': 480,
            'sm': 640,
            'md': 768,
            'lg': 1024,
            'xl': 1280,
            'xxl': 1536
        }
        self.current_breakpoint = 'lg'
        self.resize_callbacks = []

        # Bind resize event
        self.root.bind('<Configure>', self.on_window_resize)

    def add_resize_callback(self, callback: Callable[[str], None]) -> None:
        """
        Add callback to be called when window is resized.

        Args:
            callback: Function to call with current breakpoint
        """
        self.resize_callbacks.append(callback)

    def on_window_resize(self, event) -> None:
        """Handle window resize event."""
        if event.widget == self.root:
            width = self.root.winfo_width()
            new_breakpoint = self.get_breakpoint(width)

            if new_breakpoint != self.current_breakpoint:
                self.current_breakpoint = new_breakpoint
                for callback in self.resize_callbacks:
                    callback(new_breakpoint)

    def get_breakpoint(self, width: int) -> str:
        """
        Get current breakpoint based on width.

        Args:
            width: Window width

        Returns:
            Current breakpoint name
        """
        if width < self.breakpoints['xs']:
            return 'xs'
        elif width < self.breakpoints['sm']:
            return 'sm'
        elif width < self.breakpoints['md']:
            return 'md'
        elif width < self.breakpoints['lg']:
            return 'lg'
        elif width < self.breakpoints['xl']:
            return 'xl'
        else:
            return 'xxl'

    def is_mobile(self) -> bool:
        """Check if current size is mobile."""
        return self.current_breakpoint in ['xs', 'sm']

    def is_tablet(self) -> bool:
        """Check if current size is tablet."""
        return self.current_breakpoint == 'md'

    def is_desktop(self) -> bool:
        """Check if current size is desktop."""
        return self.current_breakpoint in ['lg', 'xl', 'xxl']


class ModernCard(ttk.Frame):
    """Modern card component with shadow effect simulation."""

    def __init__(self, parent, title: str = "", **kwargs):
        """
        Initialize modern card.

        Args:
            parent: Parent widget
            title: Card title
            **kwargs: Additional frame arguments
        """
        super().__init__(parent, **kwargs)

        self.configure(relief='flat', borderwidth=1)
        self.configure(style='Card.TFrame')

        # Configure grid
        self.columnconfigure(0, weight=1)

        # Add title if provided
        if title:
            title_label = ttk.Label(self, text=title, style='CardTitle.TLabel')
            title_label.grid(row=0, column=0, sticky=(tk.W, tk.E),
                           padx=ModernTheme.PADDING_LG, pady=(ModernTheme.PADDING_LG, ModernTheme.PADDING_SM))
            self.content_row = 1
        else:
            self.content_row = 0

    def add_content(self, widget, **grid_kwargs):
        """Add content to the card."""
        default_kwargs = {
            'row': self.content_row,
            'column': 0,
            'sticky': (tk.W, tk.E, tk.N, tk.S),
            'padx': ModernTheme.PADDING_LG,
            'pady': (0, ModernTheme.PADDING_LG)
        }
        default_kwargs.update(grid_kwargs)
        widget.grid(**default_kwargs)
        self.content_row += 1


class ModernButton(ttk.Button):
    """Modern button with enhanced styling."""

    def __init__(self, parent, variant: str = "primary", **kwargs):
        """
        Initialize modern button.

        Args:
            parent: Parent widget
            variant: Button variant (primary, secondary, success, warning, error)
            **kwargs: Additional button arguments
        """
        style_name = f'{variant.title()}.TButton'
        super().__init__(parent, style=style_name, **kwargs)


class ModernEntry(ttk.Frame):
    """Modern entry widget with label and validation styling."""

    def __init__(self, parent, label: str, required: bool = False, **kwargs):
        """
        Initialize modern entry.

        Args:
            parent: Parent widget
            label: Entry label
            required: Whether field is required
            **kwargs: Additional entry arguments
        """
        super().__init__(parent)

        self.columnconfigure(0, weight=1)

        # Create label
        label_text = f"{label} {'*' if required else ''}"
        self.label = ttk.Label(self, text=label_text, style='FieldLabel.TLabel')
        self.label.grid(row=0, column=0, sticky=tk.W, pady=(0, ModernTheme.PADDING_XS))

        # Create entry
        self.entry = ttk.Entry(self, style='Modern.TEntry', **kwargs)
        self.entry.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Store required status
        self.required = required

    def get(self) -> str:
        """Get entry value."""
        return self.entry.get()

    def set(self, value: str) -> None:
        """Set entry value."""
        self.entry.delete(0, tk.END)
        self.entry.insert(0, value)

    def configure_entry(self, **kwargs):
        """Configure the entry widget."""
        self.entry.configure(**kwargs)


class ModernCombobox(ttk.Frame):
    """Modern combobox widget with label."""

    def __init__(self, parent, label: str, values: list, required: bool = False, **kwargs):
        """
        Initialize modern combobox.

        Args:
            parent: Parent widget
            label: Combobox label
            values: List of values
            required: Whether field is required
            **kwargs: Additional combobox arguments
        """
        super().__init__(parent)

        self.columnconfigure(0, weight=1)

        # Create label
        label_text = f"{label} {'*' if required else ''}"
        self.label = ttk.Label(self, text=label_text, style='FieldLabel.TLabel')
        self.label.grid(row=0, column=0, sticky=tk.W, pady=(0, ModernTheme.PADDING_XS))

        # Create combobox
        self.combobox = ttk.Combobox(self, values=values, state='readonly',
                                   style='Modern.TCombobox', **kwargs)
        self.combobox.grid(row=1, column=0, sticky=(tk.W, tk.E))

        self.required = required

    def get(self) -> str:
        """Get combobox value."""
        return self.combobox.get()

    def set(self, value: str) -> None:
        """Set combobox value."""
        self.combobox.set(value)


class ModernDateEntry(ttk.Frame):
    """Modern date entry widget with format validation and helper text."""

    def __init__(self, parent, label: str, required: bool = False, **kwargs):
        """
        Initialize modern date entry.

        Args:
            parent: Parent widget
            label: Entry label
            required: Whether field is required
            **kwargs: Additional entry arguments
        """
        super().__init__(parent)

        self.columnconfigure(0, weight=1)

        # Create label
        label_text = f"{label} {'*' if required else ''}"
        self.label = ttk.Label(self, text=label_text, style='FieldLabel.TLabel')
        self.label.grid(row=0, column=0, sticky=tk.W, pady=(0, ModernTheme.PADDING_XS))

        # Create entry with placeholder
        self.entry = ttk.Entry(self, style='Modern.TEntry', **kwargs)
        self.entry.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Add format helper text
        self.helper = ttk.Label(self, text="Format: YYYY-MM-DD (e.g., 2024-01-15)",
                               style='FieldLabel.TLabel', foreground=ModernTheme.TEXT_MUTED)
        self.helper.grid(row=2, column=0, sticky=tk.W, pady=(ModernTheme.PADDING_XS, 0))

        # Store required status
        self.required = required

        # Bind validation
        self.entry.bind('<FocusOut>', self.validate_date)
        self.entry.bind('<KeyRelease>', self.format_date_input)

    def validate_date(self, event=None):
        """Validate date format on focus out."""
        value = self.entry.get().strip()
        if value:
            try:
                from datetime import datetime
                datetime.strptime(value, '%Y-%m-%d')
                self.helper.config(foreground=ModernTheme.TEXT_MUTED, text="Format: YYYY-MM-DD (e.g., 2024-01-15)")
            except ValueError:
                self.helper.config(foreground=ModernTheme.ERROR, text="Invalid date format! Use YYYY-MM-DD")

    def format_date_input(self, event=None):
        """Auto-format date input with dashes."""
        value = self.entry.get()
        # Remove any non-digit characters except dashes
        cleaned = ''.join(c for c in value if c.isdigit() or c == '-')

        # Auto-add dashes
        if len(cleaned) >= 4 and cleaned[4] != '-':
            cleaned = cleaned[:4] + '-' + cleaned[4:]
        if len(cleaned) >= 7 and cleaned[7] != '-':
            cleaned = cleaned[:7] + '-' + cleaned[7:]

        # Limit to 10 characters (YYYY-MM-DD)
        cleaned = cleaned[:10]

        if cleaned != value:
            cursor_pos = self.entry.index(tk.INSERT)
            self.entry.delete(0, tk.END)
            self.entry.insert(0, cleaned)
            # Restore cursor position
            self.entry.icursor(min(cursor_pos + (len(cleaned) - len(value)), len(cleaned)))

    def get(self) -> str:
        """Get entry value."""
        return self.entry.get()

    def set(self, value: str) -> None:
        """Set entry value."""
        self.entry.delete(0, tk.END)
        self.entry.insert(0, value)
        self.validate_date()

    def configure_entry(self, **kwargs):
        """Configure the entry widget."""
        self.entry.configure(**kwargs)


class StyleManager:
    """Manages modern styling for the application."""

    def __init__(self, root: tk.Tk):
        """
        Initialize style manager.

        Args:
            root: Root window
        """
        self.root = root
        self.style = ttk.Style()

        # Set theme
        self.style.theme_use('clam')  # Use clam as base theme

        self.setup_fonts()
        self.setup_styles()

    def setup_fonts(self) -> None:
        """Set up modern fonts with Roboto preference."""
        # Font family preferences in order
        font_families = ["Roboto", "Segoe UI", "Arial", "Helvetica", "sans-serif"]

        # Find available font family
        available_font = "Arial"  # Default fallback
        for family in font_families:
            try:
                test_font = font.Font(family=family, size=12)
                if test_font.actual("family") == family or family.lower() in test_font.actual("family").lower():
                    available_font = family
                    break
            except:
                continue

        try:
            self.default_font = font.nametofont("TkDefaultFont")
            self.default_font.configure(family=available_font, size=ModernTheme.FONT_MD)

            self.heading_font = font.Font(family=available_font, size=ModernTheme.FONT_LG, weight="bold")
            self.title_font = font.Font(family=available_font, size=ModernTheme.FONT_XL, weight="bold")
            self.small_font = font.Font(family=available_font, size=ModernTheme.FONT_SM)
            self.label_font = font.Font(family=available_font, size=ModernTheme.FONT_SM, weight="normal")

        except:
            # Fallback to default fonts
            self.default_font = font.nametofont("TkDefaultFont")
            self.heading_font = font.Font(size=ModernTheme.FONT_LG, weight="bold")
            self.title_font = font.Font(size=ModernTheme.FONT_XL, weight="bold")
            self.small_font = font.Font(size=ModernTheme.FONT_SM)
            self.label_font = font.Font(size=ModernTheme.FONT_SM)

    def setup_styles(self) -> None:
        """Set up modern styles."""
        # Configure root window
        self.root.configure(bg=ModernTheme.BACKGROUND)

        # Card styles
        self.style.configure('Card.TFrame',
                           background=ModernTheme.SURFACE,
                           relief='solid',
                           borderwidth=1)

        # Title styles
        self.style.configure('CardTitle.TLabel',
                           background=ModernTheme.SURFACE,
                           foreground=ModernTheme.TEXT_PRIMARY,
                           font=self.heading_font)

        self.style.configure('AppTitle.TLabel',
                           background=ModernTheme.BACKGROUND,
                           foreground=ModernTheme.TEXT_PRIMARY,
                           font=self.title_font)

        # Field label styles
        self.style.configure('FieldLabel.TLabel',
                           background='white',
                           foreground=ModernTheme.TEXT_SECONDARY,
                           font=self.label_font)

        # Entry styles
        self.style.configure('Modern.TEntry',
                           fieldbackground=ModernTheme.SURFACE,
                           borderwidth=1,
                           relief='solid',
                           insertcolor=ModernTheme.PRIMARY)

        self.style.map('Modern.TEntry',
                      focuscolor=[('focus', ModernTheme.BORDER_FOCUS)],
                      bordercolor=[('focus', ModernTheme.BORDER_FOCUS),
                                 ('!focus', ModernTheme.BORDER)])

        # Combobox styles
        self.style.configure('Modern.TCombobox',
                           fieldbackground=ModernTheme.SURFACE,
                           borderwidth=1,
                           relief='solid')

        # Button styles
        self.style.configure('Primary.TButton',
                           background=ModernTheme.PRIMARY,
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none')

        self.style.map('Primary.TButton',
                      background=[('active', ModernTheme.PRIMARY_DARK),
                                ('pressed', ModernTheme.PRIMARY_DARK)])

        self.style.configure('Secondary.TButton',
                           background=ModernTheme.SECONDARY,
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none')

        self.style.configure('Success.TButton',
                           background=ModernTheme.SUCCESS,
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none')

        self.style.configure('Warning.TButton',
                           background=ModernTheme.WARNING,
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none')

        self.style.configure('Error.TButton',
                           background=ModernTheme.ERROR,
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none')

        # Notebook styles
        self.style.configure('Modern.TNotebook',
                           background=ModernTheme.BACKGROUND,
                           borderwidth=0)

        self.style.configure('Modern.TNotebook.Tab',
                           background=ModernTheme.SURFACE_VARIANT,
                           foreground=ModernTheme.TEXT_SECONDARY,
                           padding=[ModernTheme.PADDING_MD, ModernTheme.PADDING_SM],
                           borderwidth=0)

        self.style.map('Modern.TNotebook.Tab',
                      background=[('selected', ModernTheme.SURFACE),
                                ('active', ModernTheme.SURFACE_VARIANT)],
                      foreground=[('selected', ModernTheme.TEXT_PRIMARY)])

        # Treeview styles
        self.style.configure('Modern.Treeview',
                           background=ModernTheme.SURFACE,
                           foreground=ModernTheme.TEXT_PRIMARY,
                           fieldbackground=ModernTheme.SURFACE,
                           borderwidth=1,
                           relief='solid')

        self.style.configure('Modern.Treeview.Heading',
                           background=ModernTheme.SURFACE_VARIANT,
                           foreground=ModernTheme.TEXT_PRIMARY,
                           relief='flat',
                           borderwidth=1)

        # Frame styles
        self.style.configure('Modern.TFrame',
                           background=ModernTheme.BACKGROUND,
                           borderwidth=0)

        self.style.configure('Surface.TFrame',
                           background=ModernTheme.SURFACE,
                           borderwidth=0)


def create_responsive_grid(parent, columns: int, breakpoint: str) -> None:
    """
    Create responsive grid configuration.

    Args:
        parent: Parent widget
        columns: Number of columns for desktop
        breakpoint: Current breakpoint
    """
    if breakpoint in ['xs', 'sm']:
        # Mobile: single column
        for i in range(1):
            parent.columnconfigure(i, weight=1)
    elif breakpoint == 'md':
        # Tablet: 2 columns
        for i in range(min(2, columns)):
            parent.columnconfigure(i, weight=1)
    else:
        # Desktop: full columns
        for i in range(columns):
            parent.columnconfigure(i, weight=1)


def get_responsive_padding(breakpoint: str) -> int:
    """
    Get responsive padding based on breakpoint.

    Args:
        breakpoint: Current breakpoint

    Returns:
        Padding value
    """
    if breakpoint in ['xs', 'sm']:
        return ModernTheme.PADDING_SM
    elif breakpoint == 'md':
        return ModernTheme.PADDING_MD
    else:
        return ModernTheme.PADDING_LG


class ExpirationValidator:
    """Validates and manages expiration notifications for data records."""

    @staticmethod
    def is_record_expired(record: Dict) -> bool:
        """
        Check if a record is expired based on date_expiration and is_expired fields.
        Only records with is_expired="No" and past expiration date are considered expired.

        Args:
            record: Dictionary containing record data

        Returns:
            True if record is expired, False otherwise
        """
        # Safely get is_expired with None handling
        is_expired_field = record.get('is_expired')
        if is_expired_field is None:
            is_expired_field = ''
        is_expired_field = str(is_expired_field).strip().lower()

        # Safely get date_expiration with None handling
        date_expiration = record.get('date_expiration')
        if date_expiration is None:
            date_expiration = ''
        date_expiration = str(date_expiration).strip()

        # Only check for expiration if is_expired is "No"
        if is_expired_field == 'no' and date_expiration:
            try:
                expiration_date = datetime.strptime(date_expiration, '%Y-%m-%d').date()
                current_date = date.today()
                return current_date > expiration_date
            except ValueError:
                # Invalid date format, consider it not expired
                return False

        return False

    @staticmethod
    def is_record_manually_expired(record: Dict) -> bool:
        """
        Check if a record is manually marked as expired (is_expired="Yes").

        Args:
            record: Dictionary containing record data

        Returns:
            True if record is manually marked as expired, False otherwise
        """
        # Safely get is_expired with None handling
        is_expired_field = record.get('is_expired')
        if is_expired_field is None:
            is_expired_field = ''
        is_expired_field = str(is_expired_field).strip().lower()
        return is_expired_field == 'yes'

    @staticmethod
    def get_expired_records(records: List[Dict]) -> List[Dict]:
        """
        Get all expired records from a list of records.

        Args:
            records: List of record dictionaries

        Returns:
            List of expired records
        """
        expired_records = []
        for record in records:
            if ExpirationValidator.is_record_expired(record):
                expired_records.append(record)
        return expired_records

    @staticmethod
    def show_expiration_notification(expired_records: List[Dict]) -> None:
        """
        Show notification for expired records (only those with is_expired="No" but past expiration date).

        Args:
            expired_records: List of expired record dictionaries
        """
        if not expired_records:
            return

        count = len(expired_records)
        if count == 1:
            record = expired_records[0]
            message = (f"⚠️ Expired Record Detected!\n\n"
                      f"Employee: {record.get('employee_name', 'Unknown')}\n"
                      f"IP Address: {record.get('ip_address', 'Unknown')}\n"
                      f"Expiration Date: {record.get('date_expiration', 'Unknown')}\n"
                      f"Current Status: Not Expired\n\n"
                      f"This record has passed its expiration date but is still marked as 'Not Expired'. "
                      f"Please review and update the status.")
        else:
            message = (f"⚠️ {count} Expired Records Detected!\n\n"
                      f"Multiple records have passed their expiration date "
                      f"but are still marked as 'Not Expired'.\n\n"
                      f"Please review and update the expiration status for these records.")

        messagebox.showwarning("Expiration Alert", message)
