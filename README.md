# 🛠️ Firewall Social Media Access - Python Desktop App

## 🎯 Objective
Create a simple and secure Python desktop application using **SQLite** to manage social media access requests within a firewall context.

---

## ✅ Functional Requirements

### 1. Main Menu
- Add a menu item titled **"Firewall Social Media Access"**.

### 2. Form for Input
Create a form to collect the following fields:
- **IP Address**
- **Employee Name**
- **Section**
- **Position**
- **Date Requested**
- **Request Type**: Dropdown with options `Temporary` or `Permanent`
- **Is Expired**: Yes / No
- **Date of Expiration** (Required if request type is `Temporary`)
- **Is IPR Reserved**: Yes / No
- **Remarks**
- **Upload Mode of Verification** (e.g., image or PDF file)

### 3. Conditional Behavior
- If **Request Type** is `Temporary`, notify the user of the **Date of Expiration**.
- If **Request Type** is `Permanent`, leave the expiration date blank and **no notification is required**.

### 4. Data Table View
- Display submitted records in a table.
- Sort by **Date Requested (Descending)**.
- Include **Update** and **Delete** buttons for each record.

### 5. Data Export Options
- **Download CSV**: Export all records in `.csv` format.
- **Download TXT**: Export records with `is_expired = "No"` in the following format:
[IP Address]; [Employee Name] - [Section] - [Position]
### 6. Filtering Options
Allow filtering of records by:
- **Date Requested**
- **IP Address**
- **Employee Name**

---

## 🧰 Tools & Stack
- **Language**: Python 3.x
- **GUI Framework**: Tkinter or PyQt
- **Database**: SQLite
- **File Handling**: Built-in Python libraries

---

## 📚 Documentation & Secure Coding

### Code Comments & Structure
- Add **clear comments** to explain all functions, logic, and UI behavior.
- Organize code into **functions** and **modules** for maintainability.

### Security Best Practices
- Input validation and sanitization
- Secure file upload handling (e.g., restrict file types to images or PDFs)
- Use **parameterized queries** to prevent SQL injection

---

## 🚀 Optimization Guidelines
- Reuse code using functions or classes
- Avoid unnecessary database queries
- Use indexing on frequently queried fields (e.g., `date_requested`)