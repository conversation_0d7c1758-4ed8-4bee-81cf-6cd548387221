"""
SSL Forms module for SSL Checker functionality.
Provides form components for SSL record management with modern UI.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import Dict, Callable, Optional
from datetime import date

from modern_ui import (
    ModernCard, ModernEntry, ModernButton, ModernTheme,
    get_responsive_padding
)
from utils import InputValidator, ValidationError, show_error_message, show_success_message


class SSLRecordForm:
    """Modern form for SSL record entry and editing with responsive design."""

    def __init__(self, parent: tk.Widget, on_submit_callback: Callable[[Dict], None],
                 responsive_manager=None):
        """
        Initialize the SSL record form.

        Args:
            parent: Parent widget
            on_submit_callback: Callback function for form submission
            responsive_manager: Responsive manager for layout adjustments
        """
        self.parent = parent
        self.on_submit_callback = on_submit_callback
        self.responsive_manager = responsive_manager
        self.current_breakpoint = 'lg'

        # Form variables
        self.application_name_var = tk.StringVar()
        self.ip_address_var = tk.StringVar()
        self.ip_url_var = tk.StringVar()
        self.remarks_var = tk.StringVar()
        self.verification_file_path = ""
        self.current_record_id = None

        self.create_form()

        # Register for responsive updates
        if self.responsive_manager:
            self.responsive_manager.add_resize_callback(self.on_breakpoint_change)

    def create_form(self) -> None:
        """Create the modern SSL record form UI elements."""
        # Configure parent grid
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(0, weight=1)

        # Main frame
        self.main_frame = ttk.Frame(self.parent, style='Modern.TFrame')
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                           padx=get_responsive_padding(self.current_breakpoint),
                           pady=get_responsive_padding(self.current_breakpoint))
        self.main_frame.columnconfigure(0, weight=1)

        # Form card
        self.form_card = ModernCard(self.main_frame, title="🔒 SSL Record Information")
        self.form_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Form content frame
        self.form_content = ttk.Frame(self.form_card, style='Surface.TFrame')
        self.form_card.add_content(self.form_content)
        self.form_content.columnconfigure(0, weight=1)
        self.form_content.columnconfigure(1, weight=1)

        self.create_form_fields()
        self.create_action_buttons()

    def create_form_fields(self) -> None:
        """Create form input fields with responsive layout."""
        row = 0

        # Application Name (Required)
        self.application_name_field = ModernEntry(
            self.form_content, "Application Name", required=True,
            textvariable=self.application_name_var
        )
        self.application_name_field.grid(row=row, column=0, columnspan=2,
                                       sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        row += 1

        # IP Address (Required)
        self.ip_address_field = ModernEntry(
            self.form_content, "IP Address", required=True,
            textvariable=self.ip_address_var
        )
        self.ip_address_field.grid(row=row, column=0, columnspan=2,
                                 sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        row += 1

        # URL (Required)
        self.ip_url_field = ModernEntry(
            self.form_content, "Application URL", required=True,
            textvariable=self.ip_url_var
        )
        self.ip_url_field.grid(row=row, column=0, columnspan=2,
                             sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        row += 1

        # Remarks
        self.remarks_field = ModernEntry(
            self.form_content, "Remarks",
            textvariable=self.remarks_var
        )
        self.remarks_field.grid(row=row, column=0, columnspan=2,
                              sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        row += 1

        # Mode of Verification (PDF Upload Only)
        verification_frame = ttk.Frame(self.form_content, style='Surface.TFrame')
        verification_frame.grid(row=row, column=0, columnspan=2,
                              sticky=(tk.W, tk.E), pady=(0, ModernTheme.PADDING_MD))
        verification_frame.columnconfigure(1, weight=1)

        # Verification label
        verification_label = ttk.Label(verification_frame, text="Mode of Verification (PDF or PNG Upload Only)",
                                     style='FieldLabel.TLabel')
        verification_label.grid(row=0, column=0, columnspan=3, sticky=tk.W,
                              pady=(0, ModernTheme.PADDING_XS))

        # File upload button
        self.upload_button = ModernButton(verification_frame, variant="secondary",
                                        text="📎 Choose PDF", command=self.choose_file)
        self.upload_button.grid(row=1, column=0, sticky=tk.W, padx=(0, ModernTheme.PADDING_SM))

        # File path display
        self.file_label = ttk.Label(verification_frame, text="No file selected",
                                  style='FieldLabel.TLabel', foreground=ModernTheme.TEXT_MUTED)
        self.file_label.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(ModernTheme.PADDING_SM, 0))

        # View file button
        self.view_file_button = ModernButton(verification_frame, variant="primary",
                                           text="👁️ View", command=self.view_file)
        self.view_file_button.grid(row=1, column=2, sticky=tk.E, padx=(ModernTheme.PADDING_SM, ModernTheme.PADDING_SM))
        self.view_file_button.configure(state='disabled')  # Initially disabled

        # Clear file button
        self.clear_file_button = ModernButton(verification_frame, variant="error",
                                            text="✖", command=self.clear_file)
        self.clear_file_button.grid(row=1, column=3, sticky=tk.E)
        row += 1

    def create_action_buttons(self) -> None:
        """Create form action buttons with responsive layout."""
        button_frame = ttk.Frame(self.form_content, style='Surface.TFrame')
        button_frame.grid(row=10, column=0, columnspan=2, pady=(ModernTheme.PADDING_LG, 0))

        if self.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.submit_button = ModernButton(button_frame, variant="primary",
                                            text="💾 Save SSL Record", command=self.submit_form)
            self.submit_button.pack(fill=tk.X, pady=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(button_frame, variant="secondary",
                                           text="🔄 Clear Form", command=self.clear_form)
            self.clear_button.pack(fill=tk.X)
        else:
            # Desktop: horizontal layout
            self.submit_button = ModernButton(button_frame, variant="primary",
                                            text="💾 Save SSL Record", command=self.submit_form)
            self.submit_button.pack(side=tk.LEFT, padx=(0, ModernTheme.PADDING_SM))

            self.clear_button = ModernButton(button_frame, variant="secondary",
                                           text="🔄 Clear Form", command=self.clear_form)
            self.clear_button.pack(side=tk.LEFT)

    def choose_file(self) -> None:
        """Open file dialog to choose PDF or PNG verification file."""
        file_path = filedialog.askopenfilename(
            title="Select PDF Verification File",
            filetypes=[("PDF files", "*.pdf"), ("PNG files", "*.png")], #Added PNG support
            defaultextension=".pdf"
        )

        if file_path:
            # Validate file type
            if not file_path.lower().endswith('.pdf') or not file_path.lower().endswith('.png'): #Added PNG support
                show_error_message("Invalid File Type",
                                 "Only PDF files are allowed for verification.")
                return

            self.verification_file_path = file_path
            # Display filename only, not full path
            filename = file_path.split('/')[-1].split('\\')[-1]
            self.file_label.config(text=f"📄 {filename}", foreground=ModernTheme.SUCCESS)
            self.view_file_button.configure(state='normal')  # Enable view button

    def view_file(self) -> None:
        """Handle view file button click."""
        if self.verification_file_path and os.path.exists(self.verification_file_path):
            from utils import FileHandler
            if FileHandler.view_file(self.verification_file_path):
                # File opened successfully
                pass
        else:
            show_error_message("File Not Found", "No file selected or file does not exist.")

    def clear_file(self) -> None:
        """Clear the selected verification file."""
        self.verification_file_path = ""
        self.file_label.config(text="No file selected", foreground=ModernTheme.TEXT_MUTED)
        self.view_file_button.configure(state='disabled')  # Disable view button

    def on_breakpoint_change(self, breakpoint: str) -> None:
        """Handle responsive breakpoint changes."""
        if breakpoint != self.current_breakpoint:
            self.current_breakpoint = breakpoint
            # Recreate action buttons with new layout
            for widget in self.form_content.winfo_children():
                if isinstance(widget, ttk.Frame) and widget.grid_info().get('row') == 10:
                    widget.destroy()
                    break
            self.create_action_buttons()

    def validate_form_data(self) -> Dict:
        """
        Validate form data and return validated dictionary.

        Returns:
            Dictionary containing validated form data

        Raises:
            ValidationError: If validation fails
        """
        validated_data = {}

        # Validate application name - handle None values safely
        app_name = self.application_name_var.get()
        app_name_str = str(app_name).strip() if app_name is not None else ''
        if not app_name_str:
            raise ValidationError("Application name is required")
        validated_data['application_name'] = app_name_str

        # Validate IP address - handle None values safely
        ip_address = self.ip_address_var.get()
        ip_address_str = str(ip_address).strip() if ip_address is not None else ''
        if not InputValidator.validate_ip_address(ip_address_str):
            raise ValidationError("Invalid IP address format")
        validated_data['ip_address'] = ip_address_str

        # Validate URL - handle None values safely
        ip_url = self.ip_url_var.get()
        ip_url_str = str(ip_url).strip() if ip_url is not None else ''
        if not ip_url_str:
            raise ValidationError("Application URL is required")
        validated_data['ip_url'] = ip_url_str

        # Optional fields - handle None values safely
        remarks = self.remarks_var.get()
        validated_data['remarks'] = str(remarks).strip() if remarks is not None else ''
        validated_data['verification_file_path'] = self.verification_file_path

        # Add current record ID if editing
        if self.current_record_id:
            validated_data['id'] = self.current_record_id

        return validated_data

    def submit_form(self) -> None:
        """Submit the form after validation."""
        try:
            validated_data = self.validate_form_data()
            self.on_submit_callback(validated_data)
        except ValidationError as e:
            show_error_message("Validation Error", str(e))
        except Exception as e:
            show_error_message("Unexpected Error", f"An unexpected error occurred: {e}")

    def clear_form(self) -> None:
        """Clear all form fields."""
        self.application_name_var.set('')
        self.ip_address_var.set('')
        self.ip_url_var.set('')
        self.remarks_var.set('')
        self.clear_file()
        self.current_record_id = None

    def load_record(self, record: Dict) -> None:
        """
        Load record data into the form for editing.

        Args:
            record: Dictionary containing record data
        """
        self.current_record_id = record.get('id')
        self.application_name_var.set(record.get('application_name', ''))
        self.ip_address_var.set(record.get('ip_address', ''))
        self.ip_url_var.set(record.get('ip_url', ''))
        self.remarks_var.set(record.get('remarks', ''))

        # Load verification file if exists
        file_path = record.get('verification_file_path', '')
        if file_path:
            self.verification_file_path = file_path
            filename = file_path.split('/')[-1].split('\\')[-1]
            self.file_label.config(text=f"📄 {filename}", foreground=ModernTheme.SUCCESS)
            self.view_file_button.configure(state='normal')  # Enable view button
        else:
            self.clear_file()
