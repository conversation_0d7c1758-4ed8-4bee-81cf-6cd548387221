"""
Threat Analyzer GUI Module

Provides the graphical user interface for the Firewall Threat Log Analyzer.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from typing import Optional, Dict, Any
from datetime import datetime

from modern_ui import (
    ModernCard, ModernButton, ModernTheme, get_responsive_padding
)
from utils import show_error_message, show_success_message, save_file_dialog
from threat_analyzer import Threat<PERSON>og<PERSON><PERSON>yzer
from threat_report_generator import ThreatReportGenerator


class ThreatAnalyzerInterface:
    """
    GUI interface for the Firewall Threat Log Analyzer.
    Provides file upload, analysis, and report generation capabilities.
    """
    
    def __init__(self, parent_frame, responsive_manager):
        """
        Initialize the threat analyzer interface.
        
        Args:
            parent_frame: Parent tkinter frame
            responsive_manager: Responsive layout manager
        """
        self.parent_frame = parent_frame
        self.responsive_manager = responsive_manager
        self.analyzer = ThreatLogAnalyzer()
        self.report_generator = ThreatReportGenerator()
        
        # Analysis state
        self.current_log_file: Optional[str] = None
        self.analysis_results: Optional[Dict[str, Any]] = None
        self.is_analyzing = False
        
        # Create the interface
        self.create_interface()
    
    def create_interface(self):
        """Create the main threat analyzer interface."""
        # Main container
        self.main_frame = ttk.Frame(self.parent_frame, style='Modern.TFrame')
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), 
                           padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
                           pady=10)
        
        # Configure grid weights
        self.parent_frame.columnconfigure(0, weight=1)
        self.parent_frame.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        
        # Create sections
        self.create_upload_section()
        self.create_analysis_section()
        self.create_results_section()
        self.create_export_section()
    
    def create_upload_section(self):
        """Create the file upload section."""
        # Upload card
        upload_card = ModernCard(self.main_frame, title="📁 Step 1: Upload Palo Alto Log File")
        upload_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Upload content
        upload_content = ttk.Frame(upload_card, style='Surface.TFrame')
        upload_card.add_content(upload_content)
        upload_content.columnconfigure(1, weight=1)
        
        # Instructions
        instructions = """
Select a Palo Alto firewall log file in CSV format. The file should contain threat log data
with standard Palo Alto fields including Source address, Destination address, Action, 
Threat/Content Type, Severity, etc.
        """
        
        instructions_label = ttk.Label(upload_content, text=instructions.strip(), 
                                     style='FieldLabel.TLabel', wraplength=500)
        instructions_label.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), 
                              pady=(0, ModernTheme.PADDING_MD))
        
        # File selection
        self.browse_button = ModernButton(upload_content, variant="primary", 
                                        text="📂 Browse CSV File", command=self.browse_log_file)
        self.browse_button.grid(row=1, column=0, sticky=tk.W, 
                              padx=(0, ModernTheme.PADDING_SM))
        
        # File path display
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("No file selected")
        
        self.file_path_label = ttk.Label(upload_content, textvariable=self.file_path_var,
                                       style='FieldLabel.TLabel', foreground=ModernTheme.TEXT_MUTED)
        self.file_path_label.grid(row=1, column=1, sticky=(tk.W, tk.E), 
                                padx=(ModernTheme.PADDING_SM, ModernTheme.PADDING_SM))
        
        # Clear file button
        self.clear_file_button = ModernButton(upload_content, variant="secondary", 
                                            text="✖ Clear", command=self.clear_file)
        self.clear_file_button.grid(row=1, column=2, sticky=tk.E)
        self.clear_file_button.configure(state='disabled')
    
    def create_analysis_section(self):
        """Create the analysis control section."""
        # Analysis card
        analysis_card = ModernCard(self.main_frame, title="🔍 Step 2: Analyze Threats")
        analysis_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Analysis content
        analysis_content = ttk.Frame(analysis_card, style='Surface.TFrame')
        analysis_card.add_content(analysis_content)
        analysis_content.columnconfigure(1, weight=1)
        
        # Analysis description
        analysis_desc = """
Click "Start Analysis" to begin threat detection and pattern analysis. The analyzer will:
• Parse and normalize log data
• Apply threat detection rules
• Perform statistical analysis
• Identify correlations and patterns
        """
        
        desc_label = ttk.Label(analysis_content, text=analysis_desc.strip(),
                             style='FieldLabel.TLabel', wraplength=500)
        desc_label.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E),
                       pady=(0, ModernTheme.PADDING_MD))
        
        # Analysis button
        self.analyze_button = ModernButton(analysis_content, variant="success",
                                         text="🚀 Start Analysis", command=self.start_analysis)
        self.analyze_button.grid(row=1, column=0, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
        self.analyze_button.configure(state='disabled')
        
        # Progress indicator
        self.progress_var = tk.StringVar()
        self.progress_var.set("Ready to analyze")
        
        self.progress_label = ttk.Label(analysis_content, textvariable=self.progress_var,
                                      style='FieldLabel.TLabel')
        self.progress_label.grid(row=1, column=1, sticky=(tk.W, tk.E),
                               padx=(ModernTheme.PADDING_SM, 0))
    
    def create_results_section(self):
        """Create the analysis results display section."""
        # Results card
        results_card = ModernCard(self.main_frame, title="📊 Step 3: Analysis Results")
        results_card.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Configure row weight for expansion
        self.main_frame.rowconfigure(2, weight=1)
        
        # Results content
        results_content = ttk.Frame(results_card, style='Surface.TFrame')
        results_card.add_content(results_content)
        results_content.columnconfigure(0, weight=1)
        results_content.rowconfigure(0, weight=1)
        
        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(
            results_content,
            wrap=tk.WORD,
            width=80,
            height=15,
            font=('Consolas', 10),
            bg=ModernTheme.SURFACE,
            fg=ModernTheme.TEXT_PRIMARY,
            insertbackground=ModernTheme.PRIMARY
        )
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                             padx=5, pady=5)
        
        # Initial message
        self.results_text.insert(tk.END, "Analysis results will appear here after running the threat analysis.\n\n")
        self.results_text.insert(tk.END, "Upload a Palo Alto log file and click 'Start Analysis' to begin.")
        self.results_text.configure(state='disabled')
    
    def create_export_section(self):
        """Create the export/download section."""
        # Export card
        export_card = ModernCard(self.main_frame, title="📄 Step 4: Generate Reports")
        export_card.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Export content
        export_content = ttk.Frame(export_card, style='Surface.TFrame')
        export_card.add_content(export_content)
        
        # Export description
        export_desc = """
Generate professional reports from the analysis results:
        """
        
        desc_label = ttk.Label(export_content, text=export_desc.strip(),
                             style='FieldLabel.TLabel')
        desc_label.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E),
                       pady=(0, ModernTheme.PADDING_MD))
        
        # Export buttons
        if self.responsive_manager.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.pdf_button = ModernButton(export_content, variant="warning",
                                         text="📑 Generate PDF Report", command=self.export_pdf)
            self.pdf_button.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E),
                               pady=(0, ModernTheme.PADDING_SM))
            
            self.csv_button = ModernButton(export_content, variant="secondary",
                                         text="📊 Export CSV Data", command=self.export_csv)
            self.csv_button.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E))
        else:
            # Desktop: side by side
            self.pdf_button = ModernButton(export_content, variant="warning",
                                         text="📑 Generate PDF Report", command=self.export_pdf)
            self.pdf_button.grid(row=1, column=0, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
            
            self.csv_button = ModernButton(export_content, variant="secondary",
                                         text="📊 Export CSV Data", command=self.export_csv)
            self.csv_button.grid(row=1, column=1, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
            
            # View results button
            self.view_button = ModernButton(export_content, variant="primary",
                                          text="👁️ View Summary", command=self.view_summary)
            self.view_button.grid(row=1, column=2, sticky=tk.W)
        
        # Initially disable export buttons
        self.pdf_button.configure(state='disabled')
        self.csv_button.configure(state='disabled')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='disabled')
    
    def browse_log_file(self):
        """Handle browse button click to select log file."""
        file_path = filedialog.askopenfilename(
            title="Select Palo Alto Log File",
            filetypes=[
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.current_log_file = file_path
            filename = os.path.basename(file_path)
            self.file_path_var.set(f"📄 {filename}")
            self.file_path_label.configure(foreground=ModernTheme.SUCCESS)
            
            # Enable analysis and clear buttons
            self.analyze_button.configure(state='normal')
            self.clear_file_button.configure(state='normal')
            
            # Reset analysis state
            self.analysis_results = None
            self.update_results_display("Log file loaded. Ready to analyze.")
            
            # Disable export buttons until analysis is complete
            self.pdf_button.configure(state='disabled')
            self.csv_button.configure(state='disabled')
            if hasattr(self, 'view_button'):
                self.view_button.configure(state='disabled')
    
    def clear_file(self):
        """Clear the selected file."""
        self.current_log_file = None
        self.analysis_results = None
        self.file_path_var.set("No file selected")
        self.file_path_label.configure(foreground=ModernTheme.TEXT_MUTED)
        
        # Disable buttons
        self.analyze_button.configure(state='disabled')
        self.clear_file_button.configure(state='disabled')
        self.pdf_button.configure(state='disabled')
        self.csv_button.configure(state='disabled')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='disabled')
        
        # Clear results
        self.update_results_display("Upload a Palo Alto log file and click 'Start Analysis' to begin.")
        self.progress_var.set("Ready to analyze")

    def start_analysis(self):
        """Start the threat analysis in a separate thread."""
        if not self.current_log_file or self.is_analyzing:
            return

        self.is_analyzing = True
        self.analyze_button.configure(state='disabled', text="🔄 Analyzing...")
        self.progress_var.set("Loading and parsing log file...")

        # Start analysis in background thread
        analysis_thread = threading.Thread(target=self._perform_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def _perform_analysis(self):
        """Perform the actual analysis (runs in background thread)."""
        try:
            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Loading log file..."))

            # Load the log file
            if not self.analyzer.load_log_file(self.current_log_file):
                self.parent_frame.after(0, lambda: self._analysis_error("Failed to load log file. Please check the file format."))
                return

            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Analyzing threats and patterns..."))

            # Perform analysis
            self.analysis_results = self.analyzer.analyze_threats()

            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Analysis complete!"))

            # Update UI on main thread
            self.parent_frame.after(0, self._analysis_complete)

        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            self.parent_frame.after(0, lambda: self._analysis_error(error_msg))

    def _analysis_complete(self):
        """Handle successful analysis completion."""
        self.is_analyzing = False
        self.analyze_button.configure(state='normal', text="🚀 Start Analysis")

        # Enable export buttons
        self.pdf_button.configure(state='normal')
        self.csv_button.configure(state='normal')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='normal')

        # Display results
        if self.analysis_results:
            summary = self.analyzer.get_analysis_summary()
            self.update_results_display(summary)

            # Show completion message
            threat_count = sum(t['count'] for t in self.analysis_results.get('threat_detections', []))
            show_success_message(
                "Analysis Complete",
                f"Threat analysis completed successfully!\n\n"
                f"• {self.analysis_results.get('summary', {}).get('total_log_entries', 0):,} log entries analyzed\n"
                f"• {threat_count:,} potential threats detected\n\n"
                f"You can now generate PDF reports or export CSV data."
            )
        else:
            self.update_results_display("Analysis completed but no results were generated.")

    def _analysis_error(self, error_message: str):
        """Handle analysis error."""
        self.is_analyzing = False
        self.analyze_button.configure(state='normal', text="🚀 Start Analysis")
        self.progress_var.set("Analysis failed")

        self.update_results_display(f"❌ Analysis Error:\n{error_message}")
        show_error_message("Analysis Error", error_message)

    def update_results_display(self, text: str):
        """Update the results text area."""
        self.results_text.configure(state='normal')
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, text)
        self.results_text.configure(state='disabled')

    def export_pdf(self):
        """Export analysis results to PDF."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to export.")
            return

        # Get save location
        filename = save_file_dialog("Save PDF Report", ".pdf")
        if not filename:
            return

        try:
            # Generate PDF report
            log_filename = os.path.basename(self.current_log_file) if self.current_log_file else "Unknown"

            if self.report_generator.generate_pdf_report(self.analysis_results, filename, log_filename):
                show_success_message(
                    "PDF Export Success",
                    f"Threat analysis report exported successfully to:\n{filename}\n\n"
                    f"The report includes detailed analysis results, statistics, and security recommendations."
                )
            else:
                show_error_message("PDF Export Error", "Failed to generate PDF report.")

        except Exception as e:
            show_error_message("PDF Export Error", f"Failed to export PDF report: {e}")

    def export_csv(self):
        """Export analysis results to CSV."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to export.")
            return

        # Get save location
        filename = save_file_dialog("Save CSV Data", ".csv")
        if not filename:
            return

        try:
            # Export to CSV
            if self.report_generator.export_analysis_to_csv(self.analysis_results, filename):
                show_success_message(
                    "CSV Export Success",
                    f"Analysis data exported successfully to:\n{filename}\n\n"
                    f"The CSV file contains summary statistics, threat detections, and recommendations."
                )
            else:
                show_error_message("CSV Export Error", "Failed to export CSV data.")

        except Exception as e:
            show_error_message("CSV Export Error", f"Failed to export CSV data: {e}")

    def view_summary(self):
        """Display analysis summary in a popup window."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to view.")
            return

        # Create summary window
        summary_window = tk.Toplevel(self.parent_frame)
        summary_window.title("Threat Analysis Summary")
        summary_window.geometry("600x500")
        summary_window.configure(bg=ModernTheme.BACKGROUND)

        # Make window modal
        summary_window.transient(self.parent_frame)
        summary_window.grab_set()

        # Summary content
        summary_frame = ttk.Frame(summary_window, style='Modern.TFrame')
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(summary_frame, text="🔍 Threat Analysis Summary",
                              style='Heading.TLabel')
        title_label.pack(pady=(0, 20))

        # Summary text
        summary_text = scrolledtext.ScrolledText(
            summary_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            bg=ModernTheme.SURFACE,
            fg=ModernTheme.TEXT_PRIMARY
        )
        summary_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Insert summary content
        summary_content = self.analyzer.get_analysis_summary()
        summary_text.insert(tk.END, summary_content)
        summary_text.configure(state='disabled')

        # Close button
        close_button = ModernButton(summary_frame, variant="secondary",
                                  text="Close", command=summary_window.destroy)
        close_button.pack()

        # Center the window
        summary_window.update_idletasks()
        x = (summary_window.winfo_screenwidth() // 2) - (summary_window.winfo_width() // 2)
        y = (summary_window.winfo_screenheight() // 2) - (summary_window.winfo_height() // 2)
        summary_window.geometry(f"+{x}+{y}")

    def refresh_layout(self):
        """Refresh the layout for responsive design."""
        # This method can be called when the window is resized
        # to update the responsive layout
        pass
