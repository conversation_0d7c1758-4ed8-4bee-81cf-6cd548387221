"""
Simple Threat Analyzer GUI

Lightweight GUI for the simplified threat analyzer using only built-in libraries.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from typing import Optional, Dict, Any
from datetime import datetime

from modern_ui import (
    ModernCard, ModernButton, ModernTheme, get_responsive_padding
)
from utils import show_error_message, show_success_message, save_file_dialog
from simple_threat_analyzer import SimpleThreatAnalyzer


class SimpleThreatInterface:
    """
    Simplified GUI interface for the lightweight threat analyzer.
    Uses only built-in Python libraries for maximum compatibility.
    """
    
    def __init__(self, parent_frame, responsive_manager):
        """
        Initialize the simple threat analyzer interface.
        
        Args:
            parent_frame: Parent tkinter frame
            responsive_manager: Responsive layout manager
        """
        self.parent_frame = parent_frame
        self.responsive_manager = responsive_manager
        self.analyzer = SimpleThreatAnalyzer()
        
        # Analysis state
        self.current_log_file: Optional[str] = None
        self.analysis_results: Optional[Dict[str, Any]] = None
        self.is_analyzing = False
        
        # Create the interface
        self.create_interface()
    
    def create_interface(self):
        """Create the main threat analyzer interface."""
        # Main container
        self.main_frame = ttk.Frame(self.parent_frame, style='Modern.TFrame')
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), 
                           padx=get_responsive_padding(self.responsive_manager.current_breakpoint),
                           pady=10)
        
        # Configure grid weights
        self.parent_frame.columnconfigure(0, weight=1)
        self.parent_frame.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        
        # Create sections
        self.create_upload_section()
        self.create_analysis_section()
        self.create_results_section()
        self.create_export_section()
    
    def create_upload_section(self):
        """Create the file upload section."""
        # Upload card
        upload_card = ModernCard(self.main_frame, title="📁 Step 1: Upload Firewall Log File")
        upload_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Upload content
        upload_content = ttk.Frame(upload_card, style='Surface.TFrame')
        upload_card.add_content(upload_content)
        upload_content.columnconfigure(1, weight=1)
        
        # Instructions
        instructions = """
Select a firewall log file in CSV format. Supports Palo Alto and other standard firewall log formats.
The analyzer will automatically detect the format and parse the data for threat analysis.
        """
        
        instructions_label = ttk.Label(upload_content, text=instructions.strip(), 
                                     style='FieldLabel.TLabel', wraplength=500)
        instructions_label.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), 
                              pady=(0, ModernTheme.PADDING_MD))
        
        # File selection
        self.browse_button = ModernButton(upload_content, variant="primary", 
                                        text="📂 Browse CSV File", command=self.browse_log_file)
        self.browse_button.grid(row=1, column=0, sticky=tk.W, 
                              padx=(0, ModernTheme.PADDING_SM))
        
        # File path display
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("No file selected")
        
        self.file_path_label = ttk.Label(upload_content, textvariable=self.file_path_var,
                                       style='FieldLabel.TLabel', foreground=ModernTheme.TEXT_MUTED)
        self.file_path_label.grid(row=1, column=1, sticky=(tk.W, tk.E), 
                                padx=(ModernTheme.PADDING_SM, ModernTheme.PADDING_SM))
        
        # Clear file button
        self.clear_file_button = ModernButton(upload_content, variant="secondary", 
                                            text="✖ Clear", command=self.clear_file)
        self.clear_file_button.grid(row=1, column=2, sticky=tk.E)
        self.clear_file_button.configure(state='disabled')
    
    def create_analysis_section(self):
        """Create the analysis control section."""
        # Analysis card
        analysis_card = ModernCard(self.main_frame, title="🔍 Step 2: Analyze Threats")
        analysis_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Analysis content
        analysis_content = ttk.Frame(analysis_card, style='Surface.TFrame')
        analysis_card.add_content(analysis_content)
        analysis_content.columnconfigure(1, weight=1)
        
        # Analysis description
        analysis_desc = """
Click "Start Analysis" to begin lightweight threat detection. The analyzer will:
• Parse CSV log data with automatic format detection
• Apply rule-based threat detection (malware, suspicious IPs, high-risk ports)
• Generate security alerts and recommendations
• Create summary statistics and top threat lists
        """
        
        desc_label = ttk.Label(analysis_content, text=analysis_desc.strip(),
                             style='FieldLabel.TLabel', wraplength=500)
        desc_label.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E),
                       pady=(0, ModernTheme.PADDING_MD))
        
        # Analysis button
        self.analyze_button = ModernButton(analysis_content, variant="success",
                                         text="🚀 Start Analysis", command=self.start_analysis)
        self.analyze_button.grid(row=1, column=0, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
        self.analyze_button.configure(state='disabled')
        
        # Progress indicator
        self.progress_var = tk.StringVar()
        self.progress_var.set("Ready to analyze")
        
        self.progress_label = ttk.Label(analysis_content, textvariable=self.progress_var,
                                      style='FieldLabel.TLabel')
        self.progress_label.grid(row=1, column=1, sticky=(tk.W, tk.E),
                               padx=(ModernTheme.PADDING_SM, 0))
    
    def create_results_section(self):
        """Create the analysis results display section."""
        # Results card
        results_card = ModernCard(self.main_frame, title="📊 Step 3: Analysis Results")
        results_card.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Configure row weight for expansion
        self.main_frame.rowconfigure(2, weight=1)
        
        # Results content
        results_content = ttk.Frame(results_card, style='Surface.TFrame')
        results_card.add_content(results_content)
        results_content.columnconfigure(0, weight=1)
        results_content.rowconfigure(0, weight=1)
        
        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(
            results_content,
            wrap=tk.WORD,
            width=80,
            height=15,
            font=('Consolas', 10),
            bg=ModernTheme.SURFACE,
            fg=ModernTheme.TEXT_PRIMARY,
            insertbackground=ModernTheme.PRIMARY
        )
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                             padx=5, pady=5)
        
        # Initial message
        self.results_text.insert(tk.END, "Analysis results will appear here after running the threat analysis.\n\n")
        self.results_text.insert(tk.END, "Upload a firewall log file and click 'Start Analysis' to begin.")
        self.results_text.configure(state='disabled')
    
    def create_export_section(self):
        """Create the export/download section."""
        # Export card
        export_card = ModernCard(self.main_frame, title="📄 Step 4: Export Reports")
        export_card.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Export content
        export_content = ttk.Frame(export_card, style='Surface.TFrame')
        export_card.add_content(export_content)
        
        # Export description
        export_desc = """
Export analysis results in simple, readable formats:
        """
        
        desc_label = ttk.Label(export_content, text=export_desc.strip(),
                             style='FieldLabel.TLabel')
        desc_label.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E),
                       pady=(0, ModernTheme.PADDING_MD))
        
        # Export buttons
        if self.responsive_manager.current_breakpoint in ['xs', 'sm']:
            # Mobile: stack buttons vertically
            self.txt_button = ModernButton(export_content, variant="warning",
                                         text="📄 Export Text Report", command=self.export_text)
            self.txt_button.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E),
                               pady=(0, ModernTheme.PADDING_SM))
            
            self.csv_button = ModernButton(export_content, variant="secondary",
                                         text="📊 Export CSV Summary", command=self.export_csv)
            self.csv_button.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E))
        else:
            # Desktop: side by side
            self.txt_button = ModernButton(export_content, variant="warning",
                                         text="📄 Export Text Report", command=self.export_text)
            self.txt_button.grid(row=1, column=0, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
            
            self.csv_button = ModernButton(export_content, variant="secondary",
                                         text="📊 Export CSV Summary", command=self.export_csv)
            self.csv_button.grid(row=1, column=1, sticky=tk.W,
                               padx=(0, ModernTheme.PADDING_SM))
            
            # View results button
            self.view_button = ModernButton(export_content, variant="primary",
                                          text="👁️ View Summary", command=self.view_summary)
            self.view_button.grid(row=1, column=2, sticky=tk.W)
        
        # Initially disable export buttons
        self.txt_button.configure(state='disabled')
        self.csv_button.configure(state='disabled')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='disabled')
    
    def browse_log_file(self):
        """Handle browse button click to select log file."""
        file_path = filedialog.askopenfilename(
            title="Select Firewall Log File",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Text files", "*.txt"),
                ("Log files", "*.log"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.current_log_file = file_path
            filename = os.path.basename(file_path)
            self.file_path_var.set(f"📄 {filename}")
            self.file_path_label.configure(foreground=ModernTheme.SUCCESS)
            
            # Enable analysis and clear buttons
            self.analyze_button.configure(state='normal')
            self.clear_file_button.configure(state='normal')
            
            # Reset analysis state
            self.analysis_results = None
            self.update_results_display("Log file loaded. Ready to analyze.")
            
            # Disable export buttons until analysis is complete
            self.txt_button.configure(state='disabled')
            self.csv_button.configure(state='disabled')
            if hasattr(self, 'view_button'):
                self.view_button.configure(state='disabled')
    
    def clear_file(self):
        """Clear the selected file."""
        self.current_log_file = None
        self.analysis_results = None
        self.file_path_var.set("No file selected")
        self.file_path_label.configure(foreground=ModernTheme.TEXT_MUTED)
        
        # Disable buttons
        self.analyze_button.configure(state='disabled')
        self.clear_file_button.configure(state='disabled')
        self.txt_button.configure(state='disabled')
        self.csv_button.configure(state='disabled')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='disabled')
        
        # Clear results
        self.update_results_display("Upload a firewall log file and click 'Start Analysis' to begin.")
        self.progress_var.set("Ready to analyze")

    def start_analysis(self):
        """Start the threat analysis in a separate thread."""
        if not self.current_log_file or self.is_analyzing:
            return

        self.is_analyzing = True
        self.analyze_button.configure(state='disabled', text="🔄 Analyzing...")
        self.progress_var.set("Loading and parsing log file...")

        # Start analysis in background thread
        analysis_thread = threading.Thread(target=self._perform_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def _perform_analysis(self):
        """Perform the actual analysis (runs in background thread)."""
        try:
            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Loading log file..."))

            # Load the log file
            if not self.analyzer.load_csv_log(self.current_log_file):
                self.parent_frame.after(0, lambda: self._analysis_error("Failed to load log file. Please check the file format."))
                return

            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Analyzing threats and patterns..."))

            # Perform analysis
            self.analysis_results = self.analyzer.analyze_threats()

            # Update progress
            self.parent_frame.after(0, lambda: self.progress_var.set("Analysis complete!"))

            # Update UI on main thread
            self.parent_frame.after(0, self._analysis_complete)

        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            self.parent_frame.after(0, lambda: self._analysis_error(error_msg))

    def _analysis_complete(self):
        """Handle successful analysis completion."""
        self.is_analyzing = False
        self.analyze_button.configure(state='normal', text="🚀 Start Analysis")

        # Enable export buttons
        self.txt_button.configure(state='normal')
        self.csv_button.configure(state='normal')
        if hasattr(self, 'view_button'):
            self.view_button.configure(state='normal')

        # Display results
        if self.analysis_results:
            summary_text = self._generate_display_summary()
            self.update_results_display(summary_text)

            # Show completion message
            summary = self.analysis_results.get('summary', {})
            threats = self.analysis_results.get('threat_detections', [])
            threat_count = sum(t['count'] for t in threats)

            show_success_message(
                "Analysis Complete",
                f"Threat analysis completed successfully!\n\n"
                f"• {summary.get('total_entries', 0):,} log entries analyzed\n"
                f"• {len(threats)} threat types detected\n"
                f"• {threat_count:,} total threat instances found\n\n"
                f"You can now export reports or view detailed results."
            )
        else:
            self.update_results_display("Analysis completed but no results were generated.")

    def _analysis_error(self, error_message: str):
        """Handle analysis error."""
        self.is_analyzing = False
        self.analyze_button.configure(state='normal', text="🚀 Start Analysis")
        self.progress_var.set("Analysis failed")

        self.update_results_display(f"❌ Analysis Error:\n{error_message}")
        show_error_message("Analysis Error", error_message)

    def _generate_display_summary(self) -> str:
        """Generate a summary for display in the results area."""
        if not self.analysis_results:
            return "No analysis results available."

        summary = self.analysis_results.get('summary', {})
        threats = self.analysis_results.get('threat_detections', [])
        alerts = self.analysis_results.get('security_alerts', [])

        display_text = f"""🔍 Firewall Threat Log Analysis Summary

📊 Overview:
• Total Log Entries: {summary.get('total_entries', 0):,}
• Unique Source IPs: {summary.get('unique_source_ips', 0):,}
• Unique Destination IPs: {summary.get('unique_destination_ips', 0):,}

🚨 Security Alerts ({len(alerts)}):
"""

        for alert in alerts:
            display_text += f"• [{alert['level']}] {alert['message']}\n"

        if not alerts:
            display_text += "• No critical security alerts detected\n"

        display_text += f"\n🔍 Threat Detection Results ({len(threats)} types):\n"

        for threat in threats:
            display_text += f"• {threat['type']}: {threat['count']} instances ({threat['severity']} priority)\n"
            display_text += f"  {threat['description']}\n"

        if not threats:
            display_text += "• No specific threats detected\n"

        # Action distribution
        action_counts = summary.get('action_counts', {})
        if action_counts:
            display_text += f"\n📈 Action Distribution:\n"
            for action, count in action_counts.items():
                display_text += f"• {action.title()}: {count:,}\n"

        # Top statistics
        stats = self.analysis_results.get('top_statistics', {})
        top_denied = stats.get('top_denied_ips', {})
        if top_denied:
            display_text += f"\n🚫 Top Denied Source IPs:\n"
            for ip, count in list(top_denied.items())[:5]:
                display_text += f"• {ip}: {count} denials\n"

        # Recommendations
        recommendations = self.analysis_results.get('recommendations', [])
        if recommendations:
            display_text += f"\n💡 Security Recommendations:\n"
            for i, rec in enumerate(recommendations[:5], 1):
                display_text += f"{i}. {rec}\n"

        return display_text

    def update_results_display(self, text: str):
        """Update the results text area."""
        self.results_text.configure(state='normal')
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, text)
        self.results_text.configure(state='disabled')

    def export_text(self):
        """Export analysis results to text file."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to export.")
            return

        # Get save location
        filename = save_file_dialog("Save Text Report", ".txt")
        if not filename:
            return

        try:
            if self.analyzer.export_simple_report(filename):
                show_success_message(
                    "Text Export Success",
                    f"Threat analysis report exported successfully to:\n{filename}\n\n"
                    f"The report includes detailed analysis results and security recommendations."
                )
            else:
                show_error_message("Text Export Error", "Failed to generate text report.")

        except Exception as e:
            show_error_message("Text Export Error", f"Failed to export text report: {e}")

    def export_csv(self):
        """Export analysis results to CSV."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to export.")
            return

        # Get save location
        filename = save_file_dialog("Save CSV Summary", ".csv")
        if not filename:
            return

        try:
            if self.analyzer.export_csv_summary(filename):
                show_success_message(
                    "CSV Export Success",
                    f"Analysis summary exported successfully to:\n{filename}\n\n"
                    f"The CSV file contains summary statistics and threat detection results."
                )
            else:
                show_error_message("CSV Export Error", "Failed to export CSV summary.")

        except Exception as e:
            show_error_message("CSV Export Error", f"Failed to export CSV summary: {e}")

    def view_summary(self):
        """Display analysis summary in a popup window."""
        if not self.analysis_results:
            show_error_message("No Results", "No analysis results available to view.")
            return

        # Create summary window
        summary_window = tk.Toplevel(self.parent_frame)
        summary_window.title("Threat Analysis Summary")
        summary_window.geometry("700x600")
        summary_window.configure(bg=ModernTheme.BACKGROUND)

        # Make window modal
        summary_window.transient(self.parent_frame)
        summary_window.grab_set()

        # Summary content
        summary_frame = ttk.Frame(summary_window, style='Modern.TFrame')
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(summary_frame, text="🛡️ Firewall Threat Analysis Summary",
                              style='Heading.TLabel')
        title_label.pack(pady=(0, 20))

        # Summary text
        summary_text = scrolledtext.ScrolledText(
            summary_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            bg=ModernTheme.SURFACE,
            fg=ModernTheme.TEXT_PRIMARY
        )
        summary_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Insert summary content
        summary_content = self._generate_display_summary()
        summary_text.insert(tk.END, summary_content)
        summary_text.configure(state='disabled')

        # Close button
        close_button = ModernButton(summary_frame, variant="secondary",
                                  text="Close", command=summary_window.destroy)
        close_button.pack()

        # Center the window
        summary_window.update_idletasks()
        x = (summary_window.winfo_screenwidth() // 2) - (summary_window.winfo_width() // 2)
        y = (summary_window.winfo_screenheight() // 2) - (summary_window.winfo_height() // 2)
        summary_window.geometry(f"+{x}+{y}")

    def refresh_layout(self):
        """Refresh the layout for responsive design."""
        # This method can be called when the window is resized
        pass
