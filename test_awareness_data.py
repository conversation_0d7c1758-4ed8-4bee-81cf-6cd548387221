"""
Test script to add sample cybersecurity awareness activity data
"""

from database import DatabaseManager
from datetime import date

def add_sample_data():
    """Add sample cybersecurity awareness activities."""
    db = DatabaseManager()
    db.create_awareness_table()
    
    sample_activities = [
        {
            'topic_subject': 'Phishing Awareness Training',
            'activity_date': '2024-01-15',
            'number_of_pax': 25,
            'pretest_result': 65.5,
            'posttest_result': 89.2,
            'remarks': 'Excellent improvement in phishing detection skills. Participants showed great engagement.'
        },
        {
            'topic_subject': 'Password Security Best Practices',
            'activity_date': '2024-01-20',
            'number_of_pax': 30,
            'pretest_result': 72.0,
            'posttest_result': 91.5,
            'remarks': 'Strong understanding of password complexity requirements. Need to reinforce 2FA adoption.'
        },
        {
            'topic_subject': 'Social Engineering Defense',
            'activity_date': '2024-01-25',
            'number_of_pax': 18,
            'pretest_result': 58.3,
            'posttest_result': 85.7,
            'remarks': 'Significant improvement in recognizing social engineering tactics. Interactive scenarios were very effective.'
        },
        {
            'topic_subject': 'Data Classification and Handling',
            'activity_date': '2024-02-01',
            'number_of_pax': 22,
            'pretest_result': 69.8,
            'posttest_result': 88.4,
            'remarks': 'Good understanding of data classification levels. Need follow-up on secure disposal procedures.'
        },
        {
            'topic_subject': 'Incident Response Procedures',
            'activity_date': '2024-02-05',
            'number_of_pax': 15,
            'pretest_result': 61.2,
            'posttest_result': 82.9,
            'remarks': 'Participants now understand escalation procedures. Practical exercises were well-received.'
        }
    ]
    
    for activity in sample_activities:
        try:
            activity_id = db.add_awareness_activity(activity)
            print(f"Added activity: {activity['topic_subject']} (ID: {activity_id})")
        except Exception as e:
            print(f"Error adding activity {activity['topic_subject']}: {e}")
    
    print(f"\nAdded {len(sample_activities)} sample awareness activities!")

if __name__ == "__main__":
    add_sample_data()
