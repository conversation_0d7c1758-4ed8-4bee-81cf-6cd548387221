"""
Database module for Firewall Social Media Access application.
Handles SQLite database operations with security best practices.
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple


class DatabaseManager:
    """Manages SQLite database operations for the application."""

    def __init__(self, db_path: str = "firewall_access.db"):
        """
        Initialize database manager.

        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self.init_database()

    def init_database(self) -> None:
        """Initialize database and create tables if they don't exist."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Create main table for access requests
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS access_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ip_address TEXT NOT NULL,
                        employee_name TEXT NOT NULL,
                        section TEXT NOT NULL,
                        position TEXT NOT NULL,
                        date_requested DATE NOT NULL,
                        request_type TEXT NOT NULL CHECK (request_type IN ('Temporary', 'Permanent')),
                        is_expired TEXT NOT NULL CHECK (is_expired IN ('Yes', 'No')),
                        date_expiration DATE,
                        is_ipr_reserved TEXT NOT NULL CHECK (is_ipr_reserved IN ('Yes', 'No')),
                        remarks TEXT,
                        verification_file_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Create indexes for frequently queried fields
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_requested ON access_requests(date_requested)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_ip_address ON access_requests(ip_address)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_employee_name ON access_requests(employee_name)')

                # Create SSL records table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ssl_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        application_name TEXT NOT NULL,
                        ip_address TEXT NOT NULL,
                        ip_url TEXT NOT NULL,
                        remarks TEXT,
                        verification_file_path TEXT,
                        ssl_valid TEXT DEFAULT 'Unknown',
                        ssl_expiration_date TEXT,
                        expiration_status TEXT NOT NULL DEFAULT 'No' CHECK (expiration_status IN ('Yes', 'No')),
                        last_scan_date TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Create indexes for SSL records
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_ssl_application_name ON ssl_records(application_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_ssl_ip_address ON ssl_records(ip_address)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_ssl_expiration_date ON ssl_records(ssl_expiration_date)')

                conn.commit()

        except sqlite3.Error as e:
            raise Exception(f"Database initialization failed: {e}")

    def add_request(self, request_data: Dict) -> int:
        """
        Add a new access request to the database.

        Args:
            request_data: Dictionary containing request information

        Returns:
            ID of the newly created record

        Raises:
            Exception: If database operation fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Use parameterized query to prevent SQL injection
                cursor.execute('''
                    INSERT INTO access_requests (
                        ip_address, employee_name, section, position, date_requested,
                        request_type, is_expired, date_expiration, is_ipr_reserved,
                        remarks, verification_file_path, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    request_data['ip_address'],
                    request_data['employee_name'],
                    request_data['section'],
                    request_data['position'],
                    request_data['date_requested'],
                    request_data['request_type'],
                    request_data['is_expired'],
                    request_data.get('date_expiration'),
                    request_data['is_ipr_reserved'],
                    request_data.get('remarks', ''),
                    request_data.get('verification_file_path', ''),
                    datetime.now().isoformat()
                ))

                conn.commit()
                return cursor.lastrowid

        except sqlite3.Error as e:
            raise Exception(f"Failed to add request: {e}")

    def get_all_requests(self, order_by: str = "date_requested DESC") -> List[Dict]:
        """
        Retrieve all access requests from the database.

        Args:
            order_by: SQL ORDER BY clause

        Returns:
            List of dictionaries containing request data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row  # Enable column access by name
                cursor = conn.cursor()

                cursor.execute(f'''
                    SELECT * FROM access_requests
                    ORDER BY {order_by}
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve requests: {e}")

    def update_request(self, request_id: int, request_data: Dict) -> bool:
        """
        Update an existing access request.

        Args:
            request_id: ID of the request to update
            request_data: Dictionary containing updated request information

        Returns:
            True if update was successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE access_requests SET
                        ip_address = ?, employee_name = ?, section = ?, position = ?,
                        date_requested = ?, request_type = ?, is_expired = ?,
                        date_expiration = ?, is_ipr_reserved = ?, remarks = ?,
                        verification_file_path = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    request_data['ip_address'],
                    request_data['employee_name'],
                    request_data['section'],
                    request_data['position'],
                    request_data['date_requested'],
                    request_data['request_type'],
                    request_data['is_expired'],
                    request_data.get('date_expiration'),
                    request_data['is_ipr_reserved'],
                    request_data.get('remarks', ''),
                    request_data.get('verification_file_path', ''),
                    datetime.now().isoformat(),
                    request_id
                ))

                conn.commit()
                return cursor.rowcount > 0

        except sqlite3.Error as e:
            raise Exception(f"Failed to update request: {e}")

    def delete_request(self, request_id: int) -> bool:
        """
        Delete an access request from the database.

        Args:
            request_id: ID of the request to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('DELETE FROM access_requests WHERE id = ?', (request_id,))

                conn.commit()
                return cursor.rowcount > 0

        except sqlite3.Error as e:
            raise Exception(f"Failed to delete request: {e}")

    def filter_requests(self, filters: Dict) -> List[Dict]:
        """
        Filter access requests based on provided criteria.

        Args:
            filters: Dictionary containing filter criteria

        Returns:
            List of dictionaries containing filtered request data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                query = "SELECT * FROM access_requests WHERE 1=1"
                params = []

                if filters.get('date_requested'):
                    query += " AND date_requested = ?"
                    params.append(filters['date_requested'])

                if filters.get('ip_address'):
                    query += " AND ip_address LIKE ?"
                    params.append(f"%{filters['ip_address']}%")

                if filters.get('employee_name'):
                    query += " AND employee_name LIKE ?"
                    params.append(f"%{filters['employee_name']}%")

                query += " ORDER BY date_requested DESC"

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to filter requests: {e}")

    def get_non_expired_requests(self) -> List[Dict]:
        """
        Get all non-expired requests for TXT export.

        Returns:
            List of dictionaries containing non-expired request data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM access_requests
                    WHERE is_expired = 'No'
                    ORDER BY date_requested DESC
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve non-expired requests: {e}")

    # SSL Records Methods

    def add_ssl_record(self, ssl_data: Dict) -> int:
        """
        Add a new SSL record to the database.

        Args:
            ssl_data: Dictionary containing SSL record information

        Returns:
            ID of the newly created record

        Raises:
            Exception: If database operation fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO ssl_records (
                        application_name, ip_address, ip_url, remarks,
                        verification_file_path, ssl_valid, ssl_expiration_date,
                        expiration_status, last_scan_date, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    ssl_data['application_name'],
                    ssl_data['ip_address'],
                    ssl_data['ip_url'],
                    ssl_data.get('remarks', ''),
                    ssl_data.get('verification_file_path', ''),
                    ssl_data.get('ssl_valid', 'Unknown'),
                    ssl_data.get('ssl_expiration_date'),
                    ssl_data.get('expiration_status', 'No'),
                    ssl_data.get('last_scan_date'),
                    datetime.now().isoformat()
                ))

                conn.commit()
                return cursor.lastrowid

        except sqlite3.Error as e:
            raise Exception(f"Failed to add SSL record: {e}")

    def get_all_ssl_records(self, order_by: str = "application_name ASC") -> List[Dict]:
        """
        Retrieve all SSL records from the database.

        Args:
            order_by: SQL ORDER BY clause

        Returns:
            List of dictionaries containing SSL record data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute(f'''
                    SELECT * FROM ssl_records
                    ORDER BY {order_by}
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve SSL records: {e}")

    def update_ssl_record(self, record_id: int, ssl_data: Dict) -> bool:
        """
        Update an existing SSL record.

        Args:
            record_id: ID of the SSL record to update
            ssl_data: Dictionary containing updated SSL record information

        Returns:
            True if update was successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE ssl_records SET
                        application_name = ?, ip_address = ?, ip_url = ?, remarks = ?,
                        verification_file_path = ?, ssl_valid = ?, ssl_expiration_date = ?,
                        expiration_status = ?, last_scan_date = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    ssl_data['application_name'],
                    ssl_data['ip_address'],
                    ssl_data['ip_url'],
                    ssl_data.get('remarks', ''),
                    ssl_data.get('verification_file_path', ''),
                    ssl_data.get('ssl_valid', 'Unknown'),
                    ssl_data.get('ssl_expiration_date'),
                    ssl_data.get('expiration_status', 'No'),
                    ssl_data.get('last_scan_date'),
                    datetime.now().isoformat(),
                    record_id
                ))

                conn.commit()
                return cursor.rowcount > 0

        except sqlite3.Error as e:
            raise Exception(f"Failed to update SSL record: {e}")

    def delete_ssl_record(self, record_id: int) -> bool:
        """
        Delete an SSL record from the database.

        Args:
            record_id: ID of the SSL record to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('DELETE FROM ssl_records WHERE id = ?', (record_id,))

                conn.commit()
                return cursor.rowcount > 0

        except sqlite3.Error as e:
            raise Exception(f"Failed to delete SSL record: {e}")

    def filter_ssl_records(self, filters: Dict) -> List[Dict]:
        """
        Filter SSL records based on provided criteria.

        Args:
            filters: Dictionary containing filter criteria

        Returns:
            List of dictionaries containing filtered SSL record data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                query = "SELECT * FROM ssl_records WHERE 1=1"
                params = []

                if filters.get('application_name'):
                    query += " AND application_name LIKE ?"
                    params.append(f"%{filters['application_name']}%")

                query += " ORDER BY application_name ASC"

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to filter SSL records: {e}")

    def get_ssl_scan_history(self, order_by: str = "last_scan_date DESC") -> List[Dict]:
        """
        Retrieve SSL scan history data from the database.
        Only returns records that have been scanned (have last_scan_date).

        Args:
            order_by: SQL ORDER BY clause

        Returns:
            List of dictionaries containing SSL scan history data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute(f'''
                    SELECT
                        id,
                        application_name,
                        ip_address,
                        ip_url,
                        ssl_valid,
                        ssl_expiration_date,
                        expiration_status,
                        last_scan_date,
                        created_at,
                        updated_at
                    FROM ssl_records
                    WHERE last_scan_date IS NOT NULL AND last_scan_date != ''
                    ORDER BY {order_by}
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve SSL scan history: {e}")

    def get_ssl_scan_statistics(self) -> Dict:
        """
        Get SSL scan statistics for reporting.

        Returns:
            Dictionary containing scan statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Total records
                cursor.execute("SELECT COUNT(*) as total FROM ssl_records")
                total_records = cursor.fetchone()['total']

                # Scanned records
                cursor.execute("SELECT COUNT(*) as scanned FROM ssl_records WHERE last_scan_date IS NOT NULL AND last_scan_date != ''")
                scanned_records = cursor.fetchone()['scanned']

                # Valid SSL certificates
                cursor.execute("SELECT COUNT(*) as valid FROM ssl_records WHERE ssl_valid = 'Valid'")
                valid_ssl = cursor.fetchone()['valid']

                # Expired SSL certificates
                cursor.execute("SELECT COUNT(*) as expired FROM ssl_records WHERE ssl_valid = 'Expired'")
                expired_ssl = cursor.fetchone()['expired']

                # Records with unknown SSL status
                cursor.execute("SELECT COUNT(*) as unknown FROM ssl_records WHERE ssl_valid = 'Unknown' OR ssl_valid IS NULL OR ssl_valid = ''")
                unknown_ssl = cursor.fetchone()['unknown']

                # Most recent scan date
                cursor.execute("SELECT MAX(last_scan_date) as latest_scan FROM ssl_records WHERE last_scan_date IS NOT NULL AND last_scan_date != ''")
                latest_scan = cursor.fetchone()['latest_scan']

                return {
                    'total_records': total_records,
                    'scanned_records': scanned_records,
                    'unscanned_records': total_records - scanned_records,
                    'valid_ssl': valid_ssl,
                    'expired_ssl': expired_ssl,
                    'unknown_ssl': unknown_ssl,
                    'latest_scan_date': latest_scan or 'Never',
                    'scan_coverage_percentage': round((scanned_records / total_records * 100) if total_records > 0 else 0, 1)
                }

        except sqlite3.Error as e:
            raise Exception(f"Failed to get SSL scan statistics: {e}")

    # Cybersecurity Awareness Activity Methods

    def create_awareness_table(self) -> None:
        """Create cybersecurity awareness activities table."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS awareness_activities (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        topic_subject TEXT NOT NULL,
                        activity_date TEXT NOT NULL,
                        number_of_pax INTEGER NOT NULL,
                        pretest_result REAL,
                        posttest_result REAL,
                        remarks TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()
        except sqlite3.Error as e:
            raise Exception(f"Failed to create awareness activities table: {e}")

    def add_awareness_activity(self, activity_data: Dict) -> int:
        """
        Add a new cybersecurity awareness activity.

        Args:
            activity_data: Dictionary containing activity information

        Returns:
            ID of the newly created activity
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO awareness_activities
                    (topic_subject, activity_date, number_of_pax, pretest_result,
                     posttest_result, remarks, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    activity_data['topic_subject'],
                    activity_data['activity_date'],
                    activity_data['number_of_pax'],
                    activity_data.get('pretest_result'),
                    activity_data.get('posttest_result'),
                    activity_data.get('remarks', ''),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.Error as e:
            raise Exception(f"Failed to add awareness activity: {e}")

    def get_all_awareness_activities(self) -> List[Dict]:
        """
        Retrieve all cybersecurity awareness activities.

        Returns:
            List of dictionaries containing activity data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM awareness_activities
                    ORDER BY activity_date DESC, created_at DESC
                ''')
                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve awareness activities: {e}")

    def get_awareness_activity_by_id(self, activity_id: int) -> Optional[Dict]:
        """
        Retrieve a specific awareness activity by ID.

        Args:
            activity_id: ID of the activity

        Returns:
            Dictionary containing activity data or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM awareness_activities WHERE id = ?', (activity_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except sqlite3.Error as e:
            raise Exception(f"Failed to retrieve awareness activity: {e}")

    def update_awareness_activity(self, activity_id: int, activity_data: Dict) -> bool:
        """
        Update an existing awareness activity.

        Args:
            activity_id: ID of the activity to update
            activity_data: Dictionary containing updated activity information

        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE awareness_activities
                    SET topic_subject = ?, activity_date = ?, number_of_pax = ?,
                        pretest_result = ?, posttest_result = ?, remarks = ?,
                        updated_at = ?
                    WHERE id = ?
                ''', (
                    activity_data['topic_subject'],
                    activity_data['activity_date'],
                    activity_data['number_of_pax'],
                    activity_data.get('pretest_result'),
                    activity_data.get('posttest_result'),
                    activity_data.get('remarks', ''),
                    datetime.now().isoformat(),
                    activity_id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.Error as e:
            raise Exception(f"Failed to update awareness activity: {e}")

    def delete_awareness_activity(self, activity_id: int) -> bool:
        """
        Delete an awareness activity.

        Args:
            activity_id: ID of the activity to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM awareness_activities WHERE id = ?', (activity_id,))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.Error as e:
            raise Exception(f"Failed to delete awareness activity: {e}")

    def filter_awareness_activities(self, filters: Dict) -> List[Dict]:
        """
        Filter awareness activities based on criteria.

        Args:
            filters: Dictionary containing filter criteria

        Returns:
            List of filtered activity records
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                query = "SELECT * FROM awareness_activities WHERE 1=1"
                params = []

                if filters.get('topic_subject'):
                    query += " AND topic_subject LIKE ?"
                    params.append(f"%{filters['topic_subject']}%")

                if filters.get('activity_date'):
                    query += " AND activity_date = ?"
                    params.append(filters['activity_date'])

                if filters.get('date_from'):
                    query += " AND activity_date >= ?"
                    params.append(filters['date_from'])

                if filters.get('date_to'):
                    query += " AND activity_date <= ?"
                    params.append(filters['date_to'])

                query += " ORDER BY activity_date DESC, created_at DESC"

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            raise Exception(f"Failed to filter awareness activities: {e}")
